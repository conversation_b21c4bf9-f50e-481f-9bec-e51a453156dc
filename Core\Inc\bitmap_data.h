/**
 * @file bitmap_data.h
 * @brief 位图数据头文件
 */

#ifndef __BITMAP_DATA_H__
#define __BITMAP_DATA_H__

#include "stm32l4xx_hal.h"

// 位图数据声明
extern const uint8_t smiley_32x32[128];
extern const uint8_t thermometer_16x16[32];
extern const uint8_t humidity_16x16[32];
extern const uint8_t wifi_16x16[32];
extern const uint8_t battery_24x12[36];
extern const uint8_t startup_screen_128x64[1024];
extern const uint8_t company_logo_64x32[256];

// 动画相关
extern const uint8_t* animation_frames[];
extern const uint8_t animation_frame_count;

// 位图尺寸定义
#define SMILEY_WIDTH        32
#define SMILEY_HEIGHT       32

#define THERMOMETER_WIDTH   16
#define THERMOMETER_HEIGHT  16

#define HUMIDITY_WIDTH      16
#define HUMIDITY_HEIGHT     16

#define WIFI_WIDTH          16
#define WIFI_HEIGHT         16

#define BATTERY_WIDTH       24
#define BATTERY_HEIGHT      12

#define LOGO_WIDTH          64
#define LOGO_HEIGHT         32

#endif // __BITMAP_DATA_H__
