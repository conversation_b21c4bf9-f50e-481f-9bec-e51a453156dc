/**
 * @file bmp_images.c
 * @brief BMP图片数据实现
 */

#include "bmp_images.h"

// 温度图标 (16x16) - 温度计形状
const uint8_t temperature_icon_16x16[] = {
    0x00, 0x00, 0x01, 0x80, 0x02, 0x40, 0x02, 0x40,
    0x02, 0x40, 0x02, 0x40, 0x02, 0x40, 0x02, 0x40,
    0x02, 0x40, 0x02, 0x40, 0x02, 0x40, 0x06, 0x60,
    0x0F, 0xF0, 0x0F, 0xF0, 0x07, 0xE0, 0x00, 0x00
};

// 湿度图标 (16x16) - 水滴形状
const uint8_t humidity_icon_16x16[] = {
    0x00, 0x00, 0x01, 0x80, 0x03, 0xC0, 0x07, 0xE0,
    0x0F, 0xF0, 0x1F, 0xF8, 0x3F, 0xFC, 0x3F, 0xFC,
    0x7F, 0xFE, 0x7F, 0xFE, 0x7F, 0xFE, 0x3F, 0xFC,
    0x3F, 0xFC, 0x1F, 0xF8, 0x07, 0xE0, 0x00, 0x00
};

// 笑脸图标 (16x16)
const uint8_t smiley_face_16x16[] = {
    0x00, 0x00, 0x0F, 0xF0, 0x18, 0x18, 0x30, 0x0C,
    0x60, 0x06, 0x66, 0x66, 0x66, 0x66, 0xC0, 0x03,
    0xC0, 0x03, 0xC6, 0x63, 0xC3, 0xC3, 0x61, 0x86,
    0x30, 0x0C, 0x18, 0x18, 0x0F, 0xF0, 0x00, 0x00
};

// 心形图标 (16x16)
const uint8_t heart_icon_16x16[] = {
    0x00, 0x00, 0x0C, 0x30, 0x1E, 0x78, 0x3F, 0xFC,
    0x7F, 0xFE, 0xFF, 0xFF, 0xFF, 0xFF, 0x7F, 0xFE,
    0x3F, 0xFC, 0x1F, 0xF8, 0x0F, 0xF0, 0x07, 0xE0,
    0x03, 0xC0, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00
};

// 启动Logo (64x32) - 简化的STM32标志
const uint8_t startup_logo_64x32[] = {
    // 第1行 (8字节)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    // 第2行
    0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFE, 0x00, 0x00,
    // 第3行
    0x00, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0x00,
    // 第4行
    0x00, 0x03, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0x00,
    // 第5行
    0x00, 0x07, 0xFF, 0x00, 0x00, 0xFF, 0xE0, 0x00,
    // 第6行
    0x00, 0x0F, 0xFE, 0x00, 0x00, 0x7F, 0xF0, 0x00,
    // 第7行
    0x00, 0x1F, 0xFC, 0x00, 0x00, 0x3F, 0xF8, 0x00,
    // 第8行
    0x00, 0x3F, 0xF8, 0x00, 0x00, 0x1F, 0xFC, 0x00,
    // 第9行
    0x00, 0x7F, 0xF0, 0x00, 0x00, 0x0F, 0xFE, 0x00,
    // 第10行
    0x00, 0xFF, 0xE0, 0x00, 0x00, 0x07, 0xFF, 0x00,
    // 第11行
    0x01, 0xFF, 0xC0, 0x00, 0x00, 0x03, 0xFF, 0x80,
    // 第12行
    0x03, 0xFF, 0x80, 0x00, 0x00, 0x01, 0xFF, 0xC0,
    // 第13行
    0x07, 0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xE0,
    // 第14行
    0x0F, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xF0,
    // 第15行
    0x1F, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF8,
    // 第16行
    0x3F, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFC,
    // 第17行
    0x7F, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xFE,
    // 第18行
    0xFF, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF,
    // 第19行
    0xFF, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFF,
    // 第20行
    0xFF, 0x80, 0x00, 0x00, 0x00, 0x00, 0x01, 0xFF,
    // 第21行
    0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF,
    // 第22行
    0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F,
    // 第23行
    0xFC, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F,
    // 第24行
    0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F,
    // 第25行
    0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F,
    // 第26行
    0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07,
    // 第27行
    0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03,
    // 第28行
    0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01,
    // 第29行
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    // 第30行
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    // 第31行
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    // 第32行
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};
