/**
 * @file custom_graphics.c
 * @brief 自定义图形和图案显示
 */

#include "ssd1306.h"
#include "ssd1306_fonts.h"

// 自定义图案数据 (16x16像素的笑脸)
const uint8_t smiley_face[32] = {
    0x00, 0x00, 0x3C, 0x00, 0x42, 0x00, 0x81, 0x00,
    0x81, 0x00, 0xA5, 0x00, 0x81, 0x00, 0x81, 0x00,
    0x81, 0x00, 0x42, 0x42, 0x24, 0x24, 0x18, 0x18,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};

// 心形图案 (16x16)
const uint8_t heart_pattern[32] = {
    0x00, 0x00, 0x0C, 0x30, 0x1E, 0x78, 0x3F, 0xFC,
    0x7F, 0xFE, 0xFF, 0xFF, 0xFF, 0xFF, 0x7F, 0xFE,
    0x3F, 0xFC, 0x1F, 0xF8, 0x0F, 0xF0, 0x07, 0xE0,
    0x03, 0xC0, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00
};

// 箭头图案 (8x8)
const uint8_t arrow_up[8] = {
    0x18, 0x3C, 0x7E, 0xFF, 0x18, 0x18, 0x18, 0x18
};

/**
 * @brief 显示自定义位图
 * @param x X坐标
 * @param y Y坐标
 * @param bitmap 位图数据
 * @param width 宽度
 * @param height 高度
 */
void OLED_DrawBitmap(uint8_t x, uint8_t y, const uint8_t* bitmap, uint8_t width, uint8_t height)
{
    for (uint8_t i = 0; i < height; i++) {
        for (uint8_t j = 0; j < width; j++) {
            uint8_t byte_index = (i * width + j) / 8;
            uint8_t bit_index = (i * width + j) % 8;
            
            if (bitmap[byte_index] & (1 << (7 - bit_index))) {
                ssd1306_DrawPixel(x + j, y + i, White);
            }
        }
    }
}

/**
 * @brief 显示进度条
 * @param x X坐标
 * @param y Y坐标
 * @param width 宽度
 * @param height 高度
 * @param progress 进度 (0-100)
 */
void OLED_DrawProgressBar(uint8_t x, uint8_t y, uint8_t width, uint8_t height, uint8_t progress)
{
    // 绘制边框
    ssd1306_DrawRectangle(x, y, x + width, y + height, White);
    
    // 计算填充宽度
    uint8_t fill_width = (width - 2) * progress / 100;
    
    // 绘制填充部分
    if (fill_width > 0) {
        ssd1306_FillRectangle(x + 1, y + 1, x + 1 + fill_width, y + height - 1, White);
    }
}

/**
 * @brief 显示数字时钟格式
 * @param x X坐标
 * @param y Y坐标
 * @param hour 小时
 * @param minute 分钟
 * @param second 秒
 */
void OLED_DisplayClock(uint8_t x, uint8_t y, uint8_t hour, uint8_t minute, uint8_t second)
{
    char time_str[16];
    sprintf(time_str, "%02d:%02d:%02d", hour, minute, second);
    ssd1306_SetCursor(x, y);
    ssd1306_WriteString(time_str, Font_11x18, White);
}

/**
 * @brief 显示温度计图案
 * @param x X坐标
 * @param y Y坐标
 * @param temperature 温度值
 */
void OLED_DrawThermometer(uint8_t x, uint8_t y, int8_t temperature)
{
    // 绘制温度计外框
    ssd1306_DrawRectangle(x, y, x + 8, y + 30, White);
    ssd1306_DrawCircle(x + 4, y + 32, 4, White);
    
    // 根据温度填充
    uint8_t fill_height = (temperature + 20) * 25 / 60; // 假设范围-20到40度
    if (fill_height > 25) fill_height = 25;
    
    for (uint8_t i = 0; i < fill_height; i++) {
        ssd1306_Line(x + 2, y + 28 - i, x + 6, y + 28 - i, White);
    }
    
    // 填充底部圆形
    ssd1306_FillCircle(x + 4, y + 32, 2, White);
    
    // 显示温度数值
    char temp_str[8];
    sprintf(temp_str, "%d°C", temperature);
    ssd1306_SetCursor(x + 15, y + 10);
    ssd1306_WriteString(temp_str, Font_7x10, White);
}

/**
 * @brief 显示电池电量图标
 * @param x X坐标
 * @param y Y坐标
 * @param level 电量等级 (0-4)
 */
void OLED_DrawBattery(uint8_t x, uint8_t y, uint8_t level)
{
    // 绘制电池外框
    ssd1306_DrawRectangle(x, y, x + 20, y + 10, White);
    ssd1306_DrawRectangle(x + 20, y + 3, x + 22, y + 7, White);
    
    // 根据电量等级填充
    for (uint8_t i = 0; i < level && i < 4; i++) {
        ssd1306_FillRectangle(x + 2 + i * 4, y + 2, x + 5 + i * 4, y + 8, White);
    }
}

/**
 * @brief 综合显示演示
 */
void OLED_CustomGraphicsDemo(void)
{
    // 清屏
    ssd1306_Fill(Black);
    
    // 显示标题
    ssd1306_SetCursor(20, 0);
    ssd1306_WriteString("Graphics", Font_7x10, White);
    
    // 显示自定义图案
    OLED_DrawBitmap(10, 15, smiley_face, 16, 16);
    OLED_DrawBitmap(40, 15, heart_pattern, 16, 16);
    
    // 显示进度条
    OLED_DrawProgressBar(70, 20, 50, 8, 75);
    
    // 显示电池图标
    OLED_DrawBattery(10, 40);
    
    // 显示温度计
    OLED_DrawThermometer(80, 35, 25);
    
    // 更新屏幕
    ssd1306_UpdateScreen();
}
