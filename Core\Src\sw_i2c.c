#include "sw_i2c.h"
#include "ssd1306_conf.h"

// 延时函数（微秒级）
static void SW_I2C_DelayUs(uint32_t us)
{
    uint32_t start = DWT->CYCCNT;
    uint32_t ticks = us * (SystemCoreClock / 1000000);
    while ((DWT->CYCCNT - start) < ticks);
}

// SCL引脚控制
static void SW_I2C_SCL_High(void)
{
    HAL_GPIO_WritePin(SSD1306_SCL_PORT, SSD1306_SCL_PIN, GPIO_PIN_SET);
}

static void SW_I2C_SCL_Low(void)
{
    HAL_GPIO_WritePin(SSD1306_SCL_PORT, SSD1306_SCL_PIN, GPIO_PIN_RESET);
}

// SDA引脚控制
static void SW_I2C_SDA_High(void)
{
    HAL_GPIO_WritePin(SSD1306_SDA_PORT, SSD1306_SDA_PIN, GPIO_PIN_SET);
}

static void SW_I2C_SDA_Low(void)
{
    HAL_GPIO_WritePin(SSD1306_SDA_PORT, SSD1306_SDA_PIN, GPIO_PIN_RESET);
}

// 读取SDA引脚状态
static uint8_t SW_I2C_SDA_Read(void)
{
    return HAL_GPIO_ReadPin(SSD1306_SDA_PORT, SSD1306_SDA_PIN);
}

// 设置SDA为输入模式
static void SW_I2C_SDA_Input(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = SSD1306_SDA_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(SSD1306_SDA_PORT, &GPIO_InitStruct);
}

// 设置SDA为输出模式
static void SW_I2C_SDA_Output(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = SSD1306_SDA_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(SSD1306_SDA_PORT, &GPIO_InitStruct);
}

// I2C初始化
void SW_I2C_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    // 使能GPIO时钟
    __HAL_RCC_GPIOA_CLK_ENABLE();
    
    // 配置SCL引脚（开漏输出）
    GPIO_InitStruct.Pin = SSD1306_SCL_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(SSD1306_SCL_PORT, &GPIO_InitStruct);
    
    // 配置SDA引脚（开漏输出）
    SW_I2C_SDA_Output();
    
    // 初始状态：SCL和SDA都为高
    SW_I2C_SCL_High();
    SW_I2C_SDA_High();
    SW_I2C_DelayUs(10);
}

// I2C开始信号
void SW_I2C_Start(void)
{
    SW_I2C_SDA_Output();
    SW_I2C_SDA_High();
    SW_I2C_SCL_High();
    SW_I2C_DelayUs(5);
    SW_I2C_SDA_Low();
    SW_I2C_DelayUs(5);
    SW_I2C_SCL_Low();
    SW_I2C_DelayUs(5);
}

// I2C停止信号
void SW_I2C_Stop(void)
{
    SW_I2C_SDA_Output();
    SW_I2C_SCL_Low();
    SW_I2C_SDA_Low();
    SW_I2C_DelayUs(5);
    SW_I2C_SCL_High();
    SW_I2C_DelayUs(5);
    SW_I2C_SDA_High();
    SW_I2C_DelayUs(5);
}

// 发送一个字节
uint8_t SW_I2C_WriteByte(uint8_t data)
{
    uint8_t i;
    uint8_t ack;
    
    SW_I2C_SDA_Output();
    
    // 发送8位数据
    for (i = 0; i < 8; i++) {
        SW_I2C_SCL_Low();
        SW_I2C_DelayUs(2);
        
        if (data & 0x80) {
            SW_I2C_SDA_High();
        } else {
            SW_I2C_SDA_Low();
        }
        
        data <<= 1;
        SW_I2C_DelayUs(2);
        SW_I2C_SCL_High();
        SW_I2C_DelayUs(5);
    }
    
    // 读取ACK
    SW_I2C_SCL_Low();
    SW_I2C_DelayUs(2);
    SW_I2C_SDA_Input();
    SW_I2C_DelayUs(2);
    SW_I2C_SCL_High();
    SW_I2C_DelayUs(2);
    ack = SW_I2C_SDA_Read();
    SW_I2C_DelayUs(2);
    SW_I2C_SCL_Low();
    SW_I2C_DelayUs(2);
    
    return ack;
}

// 写入数据到I2C设备
uint8_t SW_I2C_WriteData(uint8_t addr, uint8_t *data, uint16_t len)
{
    uint16_t i;
    
    SW_I2C_Start();
    
    // 发送设备地址
    if (SW_I2C_WriteByte(addr) != 0) {
        SW_I2C_Stop();
        return 1; // NACK
    }
    
    // 发送数据
    for (i = 0; i < len; i++) {
        if (SW_I2C_WriteByte(data[i]) != 0) {
            SW_I2C_Stop();
            return 1; // NACK
        }
    }
    
    SW_I2C_Stop();
    return 0; // ACK
}
