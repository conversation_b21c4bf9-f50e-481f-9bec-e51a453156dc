/**
 * @file bitmap_display.c
 * @brief OLED位图显示功能
 */

#include "bitmap_display.h"
#include "ssd1306.h"
#include <string.h>

/**
 * @brief 显示全屏位图
 * @param bitmap 位图数据指针
 */
void OLED_DisplayBitmap(const uint8_t* bitmap)
{
    // 直接将位图数据复制到SSD1306缓冲区
    memcpy(SSD1306_Buffer, bitmap, SSD1306_BUFFER_SIZE);
    
    // 更新屏幕显示
    ssd1306_UpdateScreen();
}

/**
 * @brief 显示指定位置和尺寸的位图
 * @param x X坐标
 * @param y Y坐标  
 * @param width 宽度
 * @param height 高度
 * @param bitmap 位图数据
 */
void OLED_DisplayBitmapAt(uint8_t x, uint8_t y, uint8_t width, uint8_t height, const uint8_t* bitmap)
{
    for (uint8_t j = 0; j < height; j++) {
        for (uint8_t i = 0; i < width; i++) {
            // 计算在位图数据中的位置
            uint16_t bitmap_index = (j * width + i) / 8;
            uint8_t bitmap_bit = (j * width + i) % 8;
            
            // 检查该像素是否应该点亮
            if (bitmap[bitmap_index] & (1 << (7 - bitmap_bit))) {
                ssd1306_DrawPixel(x + i, y + j, White);
            } else {
                ssd1306_DrawPixel(x + i, y + j, Black);
            }
        }
    }
}

/**
 * @brief 显示带透明背景的位图
 * @param x X坐标
 * @param y Y坐标
 * @param width 宽度
 * @param height 高度
 * @param bitmap 位图数据
 */
void OLED_DisplayBitmapTransparent(uint8_t x, uint8_t y, uint8_t width, uint8_t height, const uint8_t* bitmap)
{
    for (uint8_t j = 0; j < height; j++) {
        for (uint8_t i = 0; i < width; i++) {
            // 计算在位图数据中的位置
            uint16_t bitmap_index = (j * width + i) / 8;
            uint8_t bitmap_bit = (j * width + i) % 8;
            
            // 只绘制白色像素，黑色像素保持透明
            if (bitmap[bitmap_index] & (1 << (7 - bitmap_bit))) {
                ssd1306_DrawPixel(x + i, y + j, White);
            }
            // 不绘制黑色像素，保持原有内容
        }
    }
}

/**
 * @brief 显示反色位图
 * @param bitmap 位图数据指针
 */
void OLED_DisplayBitmapInverted(const uint8_t* bitmap)
{
    // 复制并反转每个字节
    for (uint16_t i = 0; i < SSD1306_BUFFER_SIZE; i++) {
        SSD1306_Buffer[i] = ~bitmap[i];  // 按位取反
    }
    
    // 更新屏幕显示
    ssd1306_UpdateScreen();
}

/**
 * @brief 滚动显示位图
 * @param bitmap 位图数据
 * @param direction 滚动方向 (0:左, 1:右, 2:上, 3:下)
 * @param speed 滚动速度 (延时ms)
 */
void OLED_ScrollBitmap(const uint8_t* bitmap, uint8_t direction, uint16_t speed)
{
    switch (direction) {
        case 0: // 向左滚动
            for (int16_t offset = SSD1306_WIDTH; offset >= -SSD1306_WIDTH; offset -= 2) {
                ssd1306_Fill(Black);
                OLED_DisplayBitmapAt(offset, 0, SSD1306_WIDTH, SSD1306_HEIGHT, bitmap);
                ssd1306_UpdateScreen();
                HAL_Delay(speed);
            }
            break;
            
        case 1: // 向右滚动
            for (int16_t offset = -SSD1306_WIDTH; offset <= SSD1306_WIDTH; offset += 2) {
                ssd1306_Fill(Black);
                OLED_DisplayBitmapAt(offset, 0, SSD1306_WIDTH, SSD1306_HEIGHT, bitmap);
                ssd1306_UpdateScreen();
                HAL_Delay(speed);
            }
            break;
            
        case 2: // 向上滚动
            for (int16_t offset = SSD1306_HEIGHT; offset >= -SSD1306_HEIGHT; offset -= 1) {
                ssd1306_Fill(Black);
                OLED_DisplayBitmapAt(0, offset, SSD1306_WIDTH, SSD1306_HEIGHT, bitmap);
                ssd1306_UpdateScreen();
                HAL_Delay(speed);
            }
            break;
            
        case 3: // 向下滚动
            for (int16_t offset = -SSD1306_HEIGHT; offset <= SSD1306_HEIGHT; offset += 1) {
                ssd1306_Fill(Black);
                OLED_DisplayBitmapAt(0, offset, SSD1306_WIDTH, SSD1306_HEIGHT, bitmap);
                ssd1306_UpdateScreen();
                HAL_Delay(speed);
            }
            break;
    }
}

/**
 * @brief 淡入淡出效果显示位图
 * @param bitmap 位图数据
 * @param fade_in 是否淡入 (true:淡入, false:淡出)
 */
void OLED_FadeBitmap(const uint8_t* bitmap, bool fade_in)
{
    if (fade_in) {
        // 淡入效果
        for (uint8_t level = 0; level <= 10; level++) {
            ssd1306_Fill(Black);
            
            // 根据level随机显示像素
            for (uint16_t i = 0; i < SSD1306_BUFFER_SIZE; i++) {
                uint8_t byte_data = bitmap[i];
                uint8_t result = 0;
                
                for (uint8_t bit = 0; bit < 8; bit++) {
                    if (byte_data & (1 << bit)) {
                        // 根据level决定是否显示该像素
                        if ((i * 8 + bit) % 10 < level) {
                            result |= (1 << bit);
                        }
                    }
                }
                SSD1306_Buffer[i] = result;
            }
            
            ssd1306_UpdateScreen();
            HAL_Delay(100);
        }
    } else {
        // 淡出效果
        for (uint8_t level = 10; level > 0; level--) {
            ssd1306_Fill(Black);
            
            for (uint16_t i = 0; i < SSD1306_BUFFER_SIZE; i++) {
                uint8_t byte_data = bitmap[i];
                uint8_t result = 0;
                
                for (uint8_t bit = 0; bit < 8; bit++) {
                    if (byte_data & (1 << bit)) {
                        if ((i * 8 + bit) % 10 < level) {
                            result |= (1 << bit);
                        }
                    }
                }
                SSD1306_Buffer[i] = result;
            }
            
            ssd1306_UpdateScreen();
            HAL_Delay(100);
        }
    }
}
