/**
 * @file string_display_example.c
 * @brief ssd1306_WriteString函数工作原理示例
 */

#include "ssd1306.h"
#include "ssd1306_fonts.h"

/**
 * @brief 演示ssd1306_WriteString函数的工作过程
 */
void Demo_WriteString_Process(void)
{
    char* demo_string = "Hello";
    
    // 假设我们要显示字符串"Hello"
    // 函数执行过程如下：
    
    // 第1次循环: *str = 'H'
    // 调用 ssd1306_WriteChar('H', Font_11x18, White)
    // 如果成功，继续；如果失败，返回'H'
    
    // 第2次循环: *str = 'e'  
    // 调用 ssd1306_WriteChar('e', Font_11x18, White)
    // 如果成功，继续；如果失败，返回'e'
    
    // 第3次循环: *str = 'l'
    // 调用 ssd1306_WriteChar('l', Font_11x18, White)
    // 如果成功，继续；如果失败，返回'l'
    
    // 第4次循环: *str = 'l'
    // 调用 ssd1306_WriteChar('l', Font_11x18, White)
    // 如果成功，继续；如果失败，返回'l'
    
    // 第5次循环: *str = 'o'
    // 调用 ssd1306_WriteChar('o', Font_11x18, White)
    // 如果成功，继续；如果失败，返回'o'
    
    // 第6次循环: *str = '\0' (字符串结束符)
    // while条件为false，退出循环
    // 返回 '\0' (ASCII值为0)，表示成功
}

/**
 * @brief 使用示例
 */
void Example_Usage(void)
{
    char result;
    
    // 设置显示位置
    ssd1306_SetCursor(10, 20);
    
    // 显示字符串
    result = ssd1306_WriteString("Temperature", Font_11x18, White);
    
    // 检查结果
    if (result == 0) {
        // 成功显示整个字符串
        // result == '\0' (ASCII值为0)
    } else {
        // 显示失败，result包含失败的字符
        // 例如：如果在显示'e'时失败，result = 'e'
    }
}

/**
 * @brief 错误处理示例
 */
void Error_Handling_Example(void)
{
    char result;
    char error_msg[50];
    
    ssd1306_SetCursor(0, 0);
    result = ssd1306_WriteString("Very Long String That Might Exceed Screen", Font_11x18, White);
    
    if (result != 0) {
        // 显示失败
        sprintf(error_msg, "Failed at char: %c (ASCII: %d)", result, result);
        
        // 在另一个位置显示错误信息
        ssd1306_SetCursor(0, 30);
        ssd1306_WriteString(error_msg, Font_6x8, White);
    }
}

/**
 * @brief 多行文本显示示例
 */
void Multi_Line_Display_Example(void)
{
    char result;
    
    // 第一行
    ssd1306_SetCursor(2, 0);
    result = ssd1306_WriteString("Line 1", Font_11x18, White);
    
    if (result == 0) {
        // 第二行
        ssd1306_SetCursor(2, 20);
        result = ssd1306_WriteString("Line 2", Font_7x10, White);
    }
    
    if (result == 0) {
        // 第三行
        ssd1306_SetCursor(2, 35);
        result = ssd1306_WriteString("Line 3", Font_6x8, White);
    }
    
    // 更新屏幕显示
    ssd1306_UpdateScreen();
}

/**
 * @brief 动态字符串显示示例
 */
void Dynamic_String_Example(void)
{
    char temp_str[32];
    char result;
    int temperature = 25;
    int humidity = 60;
    
    // 格式化字符串
    sprintf(temp_str, "T:%dC H:%d%%", temperature, humidity);
    
    // 显示动态生成的字符串
    ssd1306_SetCursor(2, 0);
    result = ssd1306_WriteString(temp_str, Font_11x18, White);
    
    if (result == 0) {
        // 显示状态信息
        ssd1306_SetCursor(2, 25);
        ssd1306_WriteString("Status: OK", Font_7x10, White);
    } else {
        // 显示错误
        ssd1306_SetCursor(2, 25);
        ssd1306_WriteString("Display Error", Font_7x10, White);
    }
    
    ssd1306_UpdateScreen();
}
