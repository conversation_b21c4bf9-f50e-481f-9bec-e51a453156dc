#ifndef __SSD1306_CONF_H__
#define __SSD1306_CONF_H__

// 选择通信接口
#define SSD1306_USE_I2C

// I2C 配置
#define SSD1306_I2C_PORT        hi2c1
#define SSD1306_I2C_ADDR        (0x3C << 1)

// 屏幕尺寸配置
#define SSD1306_WIDTH           128
#define SSD1306_HEIGHT          64

// 字体配置
#define SSD1306_INCLUDE_FONT_6x8
#define SSD1306_INCLUDE_FONT_7x10
#define SSD1306_INCLUDE_FONT_11x18
#define SSD1306_INCLUDE_FONT_16x26

// 缓冲区大小
#define SSD1306_BUFFER_SIZE     SSD1306_WIDTH * SSD1306_HEIGHT / 8

#endif /* __SSD1306_CONF_H__ */
