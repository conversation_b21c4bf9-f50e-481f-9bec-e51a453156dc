#include "dht11.h"

/**
 * @brief DHT11初始化函数
 */
void DHT11_Init(void)
{
	// 配置GPIO为开漏输出模式，带上拉
	GPIO_InitTypeDef GPIO_InitStruct = {0};
	GPIO_InitStruct.Pin = DHT11_PIN;
	GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD;
	GPIO_InitStruct.Pull = GPIO_PULLUP;
	GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
	HAL_GPIO_Init(DHT11_PORT, &GPIO_InitStruct);

	// 设置初始状态为高电平
	HAL_GPIO_WritePin(DHT11_PORT, DHT11_PIN, GPIO_PIN_SET);

	// 等待DHT11稳定
	HAL_Delay(1000);
}

static void DHT11_SetPinOutput(void)
{
	GPIO_InitTypeDef GPIO_InitStruct = {0};
	GPIO_InitStruct.Pin = DHT11_PIN;
	GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD; // 开漏输出
	GPIO_InitStruct.Pull = GPIO_PULLUP; // 上拉
	GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
	HAL_GPIO_Init(DHT11_PORT, &GPIO_InitStruct);
}

static void DHT11_SetPinInput(void)
{
	GPIO_InitTypeDef GPIO_InitStruct = {0};
	GPIO_InitStruct.Pin = DHT11_PIN;
	GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
	GPIO_InitStruct.Pull = GPIO_PULLUP; // 上拉
	HAL_GPIO_Init(DHT11_PORT, &GPIO_InitStruct);
}

static void DHT11_DelayUs(uint32_t us)
{
	uint32_t start = DWT->CYCCNT;
	uint32_t ticks = us * (SystemCoreClock / 1000000);
	while ((DWT->CYCCNT - start) < ticks);
}

uint8_t DHT11_Read(uint8_t *temp, uint8_t *humi)
{
	uint8_t data[5] = {0};
	uint32_t timeout = 0;

	// 发送开始信号
	DHT11_SetPinOutput();
	HAL_GPIO_WritePin(DHT11_PORT, DHT11_PIN, GPIO_PIN_RESET);
	HAL_Delay(20); // 延长到20ms，确保DHT11检测到
	HAL_GPIO_WritePin(DHT11_PORT, DHT11_PIN, GPIO_PIN_SET);
	DHT11_DelayUs(40); // 延长等待时间
	DHT11_SetPinInput();

	// 等待DHT11响应 - 等待低电平
	timeout = 0;
	while (HAL_GPIO_ReadPin(DHT11_PORT, DHT11_PIN) == GPIO_PIN_SET) {
		if (++timeout > 500) return 1; // 增加超时时间
		DHT11_DelayUs(2);
	}

	// 等待DHT11响应 - 等待高电平
	timeout = 0;
	while (HAL_GPIO_ReadPin(DHT11_PORT, DHT11_PIN) == GPIO_PIN_RESET) {
		if (++timeout > 500) return 2;
		DHT11_DelayUs(2);
	}

	// 等待DHT11响应结束 - 等待低电平
	timeout = 0;
	while (HAL_GPIO_ReadPin(DHT11_PORT, DHT11_PIN) == GPIO_PIN_SET) {
		if (++timeout > 500) return 3;
		DHT11_DelayUs(2);
	}

	// 读取40位数据
	for (int i = 0; i < 40; i++) {
		// 等待数据位开始（低电平结束）
		timeout = 0;
		while (HAL_GPIO_ReadPin(DHT11_PORT, DHT11_PIN) == GPIO_PIN_RESET) {
			if (++timeout > 500) return 4;
			DHT11_DelayUs(2);
		}

		// 延时30us后检测电平，判断是0还是1
		DHT11_DelayUs(30);
		if (HAL_GPIO_ReadPin(DHT11_PORT, DHT11_PIN) == GPIO_PIN_SET) {
			data[i / 8] |= (1 << (7 - (i % 8))); // 设置对应位为1
		}

		// 等待高电平结束
		timeout = 0;
		while (HAL_GPIO_ReadPin(DHT11_PORT, DHT11_PIN) == GPIO_PIN_SET) {
			if (++timeout > 500) return 5;
			DHT11_DelayUs(2);
		}
	}

	// 校验数据
	uint8_t checksum = data[0] + data[1] + data[2] + data[3];
	if (data[4] == checksum) {
		*humi = data[0]; // 湿度整数部分
		*temp = data[2]; // 温度整数部分
		return 0; // 成功
	}
	return 6; // 校验失败
}
