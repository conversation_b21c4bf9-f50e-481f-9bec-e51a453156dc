#include "dht11.h"

static void DHT11_SetPinOutput(void)
{
	GPIO_InitTypeDef GPIO_InitStruct = {0};
	GPIO_InitStruct.Pin = DHT11_PIN;
	GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
	GPIO_InitStruct.Pull = GPIO_NOPULL;
	HAL_GPIO_Init(DHT11_PORT, &GPIO_InitStruct);
}

static void DHT11_SetPinInput(void)
{
	GPIO_InitTypeDef GPIO_InitStruct = {0};
	GPIO_InitStruct.Pin = DHT11_PIN;
	GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
	GPIO_InitStruct.Pull = GPIO_NOPULL;
	HAL_GPIO_Init(DHT11_PORT, &GPIO_InitStruct);
}

static void DHT11_DelayUs(uint32_t us)
{
	uint32_t start = DWT->CYCCNT;
	uint32_t ticks = us * (SystemCoreClock / 1000000);
	while ((DWT->CYCCNT - start) < ticks);
}

uint8_t DHT11_Read(uint8_t *temp, uint8_t *humi)
{
	uint8_t data[5] = {0};
	uint32_t timeout = 0;

	DHT11_SetPinOutput();
	HAL_GPIO_WritePin(DHT11_PORT, DHT11_PIN, GPIO_PIN_RESET);
	HAL_Delay(18);
	HAL_GPIO_WritePin(DHT11_PORT, DHT11_PIN, GPIO_PIN_SET);
	DHT11_DelayUs(30);
	DHT11_SetPinInput();

	timeout = 0;
	while (HAL_GPIO_ReadPin(DHT11_PORT, DHT11_PIN) == GPIO_PIN_SET) {
		if (++timeout > 100) return 1;
		DHT11_DelayUs(1);
	}
	timeout = 0;
	while (HAL_GPIO_ReadPin(DHT11_PORT, DHT11_PIN) == GPIO_PIN_RESET) {
		if (++timeout > 100) return 2;
		DHT11_DelayUs(1);
	}
	timeout = 0;
	while (HAL_GPIO_ReadPin(DHT11_PORT, DHT11_PIN) == GPIO_PIN_SET) {
		if (++timeout > 100) return 3;
		DHT11_DelayUs(1);
	}

	for (int i = 0; i < 40; i++) {
		timeout = 0;
		while (HAL_GPIO_ReadPin(DHT11_PORT, DHT11_PIN) == GPIO_PIN_RESET) {
			if (++timeout > 100) return 4;
			DHT11_DelayUs(1);
		}
		DHT11_DelayUs(40);
		if (HAL_GPIO_ReadPin(DHT11_PORT, DHT11_PIN) == GPIO_PIN_SET) {
			data[i / 8] |= (1 << (7 - (i % 8)));
			timeout = 0;
			while (HAL_GPIO_ReadPin(DHT11_PORT, DHT11_PIN) == GPIO_PIN_SET) {
				if (++timeout > 100) return 5;
				DHT11_DelayUs(1);
			}
		}
	}

	if (data[4] == (data[0] + data[1] + data[2] + data[3])) {
		*humi = data[0];
		*temp = data[2];
		return 0;
	}
	return 6;
}
