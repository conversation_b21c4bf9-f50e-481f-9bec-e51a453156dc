/**
 * @file bmp_images.h
 * @brief BMP图片数据定义
 */

#ifndef __BMP_IMAGES_H__
#define __BMP_IMAGES_H__

#include "stdint.h"

// 温度图标 (16x16)
extern const uint8_t temperature_icon_16x16[];

// 湿度图标 (16x16)  
extern const uint8_t humidity_icon_16x16[];

// 笑脸图标 (16x16)
extern const uint8_t smiley_face_16x16[];

// 心形图标 (16x16)
extern const uint8_t heart_icon_16x16[];

// 启动Logo (64x32)
extern const uint8_t startup_logo_64x32[];

// 图片尺寸定义
#define TEMP_ICON_WIDTH     16
#define TEMP_ICON_HEIGHT    16
#define HUMIDITY_ICON_WIDTH 16
#define HUMIDITY_ICON_HEIGHT 16
#define SMILEY_WIDTH        16
#define SMILEY_HEIGHT       16
#define HEART_WIDTH         16
#define HEART_HEIGHT        16
#define LOGO_WIDTH          64
#define LOGO_HEIGHT         32

#endif // __BMP_IMAGES_H__
