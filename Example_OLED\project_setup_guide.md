# STM32L432KC + OLED 项目配置指南

## 1. CubeMX 配置步骤

### 芯片选择
- 选择 STM32L432KCUx
- Package: UFQFPN32

### 引脚配置
```
PA9  → I2C1_SCL
PA10 → I2C1_SDA
```

### I2C1 配置
```
Mode: I2C
I2C Speed Mode: Standard Mode
I2C Clock Speed: 100000 Hz
Own Address Length: 7-bit
Primary Address Length 7-bit: 0
Dual Address Acknowledged: Disabled
Primary slave Address: 0
General Call Address Detection: Disabled
Clock No Stretch Mode: Disabled
```

### 时钟配置
```
Input frequency: 4 MHz (MSI)
SYSCLK: 80 MHz
HCLK: 80 MHz
PCLK1: 80 MHz
PCLK2: 80 MHz
```

### 项目设置
```
Project Name: OLED_Display_L432KC
Toolchain/IDE: MDK-ARM V5
Firmware Package Name and Version: STM32Cube FW_L4 V1.17.0
```

## 2. Keil 项目配置

### 添加源文件
在 Keil 项目中添加以下文件：
```
Application/User/Core/
├── main.c (CubeMX生成)
├── ssd1306.c
├── ssd1306_fonts.c
├── custom_graphics.c (可选)
└── animations.c (可选)
```

### 添加头文件
```
Application/User/Core/
├── main.h (CubeMX生成)
├── ssd1306.h
├── ssd1306_fonts.h
└── ssd1306_conf.h
```

### 编译器设置
```
C/C++ → Define:
- USE_HAL_DRIVER
- STM32L432xx

C/C++ → Include Paths:
- ../Core/Inc
- ../Drivers/STM32L4xx_HAL_Driver/Inc
- ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy
- ../Drivers/CMSIS/Device/ST/STM32L4xx/Include
- ../Drivers/CMSIS/Include
```

## 3. 硬件连接

### OLED 模块连接
```
OLED    STM32L432KC
GND  →  GND
VCC  →  3.3V
SCL  →  PA9  (I2C1_SCL)
SDA  →  PA10 (I2C1_SDA)
```

### 注意事项
1. 确保使用3.3V供电，不要使用5V
2. OLED模块通常自带上拉电阻，无需外加
3. I2C地址通常为0x3C或0x3D，根据模块调整

## 4. 代码集成

### main.c 中的关键代码
```c
#include "ssd1306.h"
#include "ssd1306_fonts.h"

int main(void)
{
    // ... HAL初始化代码 ...
    
    // 初始化OLED
    ssd1306_Init();
    
    // 清屏
    ssd1306_Fill(Black);
    ssd1306_UpdateScreen();
    
    while(1)
    {
        // 显示文字
        ssd1306_Fill(Black);
        ssd1306_SetCursor(2, 0);
        ssd1306_WriteString("Hello World!", Font_11x18, White);
        ssd1306_UpdateScreen();
        
        HAL_Delay(1000);
    }
}
```

## 5. 常见问题解决

### 编译错误
1. 确保所有头文件路径正确
2. 检查 ssd1306_conf.h 配置
3. 确认 I2C 句柄名称匹配

### 显示问题
1. 检查硬件连接
2. 确认 I2C 地址正确
3. 检查电源供电

### 性能优化
1. 减少不必要的屏幕更新
2. 使用局部更新而非全屏更新
3. 优化图形绘制算法

## 6. 扩展功能

### 添加更多字体
在 ssd1306_fonts.c 中添加自定义字体

### 添加图片显示
使用位图数据显示图片

### 添加菜单系统
实现多级菜单导航

### 添加传感器数据显示
结合温湿度传感器等显示实时数据
