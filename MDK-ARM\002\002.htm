<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [002\002.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image 002\002.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Mon Jun 02 23:48:10 2025
<BR><P>
<H3>Maximum Stack Usage =        232 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
__rt_entry_main &rArr; main &rArr; ssd1306_Init &rArr; ssd1306_UpdateScreen &rArr; ssd1306_WriteData &rArr; SW_I2C_WriteData &rArr; SW_I2C_WriteByte &rArr; SW_I2C_SDA_Output &rArr; HAL_GPIO_Init
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[7d]">__user_initial_stackheap</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[23]">ADC1_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[23]">ADC1_IRQHandler</a><BR>
 <LI><a href="#[b]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[b]">BusFault_Handler</a><BR>
 <LI><a href="#[9]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[9]">HardFault_Handler</a><BR>
 <LI><a href="#[a]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[a]">MemManage_Handler</a><BR>
 <LI><a href="#[8]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[8]">NMI_Handler</a><BR>
 <LI><a href="#[c]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[c]">UsageFault_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[23]">ADC1_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[b]">BusFault_Handler</a> from stm32l4xx_it.o(i.BusFault_Handler) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[25]">CAN1_RX0_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[26]">CAN1_RX1_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[27]">CAN1_SCE_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[24]">CAN1_TX_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[3d]">COMP_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[4c]">CRS_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[1c]">DMA1_Channel1_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[1d]">DMA1_Channel2_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[1e]">DMA1_Channel3_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[1f]">DMA1_Channel4_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[20]">DMA1_Channel5_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[21]">DMA1_Channel6_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[22]">DMA1_Channel7_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[38]">DMA2_Channel1_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[39]">DMA2_Channel2_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[3a]">DMA2_Channel3_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[3b]">DMA2_Channel4_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[3c]">DMA2_Channel5_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[41]">DMA2_Channel6_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[42]">DMA2_Channel7_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[e]">DebugMon_Handler</a> from stm32l4xx_it.o(i.DebugMon_Handler) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[17]">EXTI0_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[33]">EXTI15_10_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[18]">EXTI1_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[19]">EXTI2_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[1a]">EXTI3_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[1b]">EXTI4_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[28]">EXTI9_5_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[15]">FLASH_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[4b]">FPU_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[9]">HardFault_Handler</a> from stm32l4xx_it.o(i.HardFault_Handler) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[2f]">I2C1_ER_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[2e]">I2C1_EV_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[46]">I2C3_ER_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[45]">I2C3_EV_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[3e]">LPTIM1_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[3f]">LPTIM2_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[43]">LPUART1_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[a]">MemManage_Handler</a> from stm32l4xx_it.o(i.MemManage_Handler) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[8]">NMI_Handler</a> from stm32l4xx_it.o(i.NMI_Handler) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[12]">PVD_PVM_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[f]">PendSV_Handler</a> from stm32l4xx_it.o(i.PendSV_Handler) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[44]">QUADSPI_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[16]">RCC_IRQHandler</a> from stm32l4xx_it.o(i.RCC_IRQHandler) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[4a]">RNG_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[34]">RTC_Alarm_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[14]">RTC_WKUP_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[7]">Reset_Handler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[47]">SAI1_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[30]">SPI1_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[35]">SPI3_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[d]">SVC_Handler</a> from stm32l4xx_it.o(i.SVC_Handler) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[48]">SWPMI1_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[10]">SysTick_Handler</a> from stm32l4xx_it.o(i.SysTick_Handler) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[4d]">SystemInit</a> from system_stm32l4xx.o(i.SystemInit) referenced from startup_stm32l432xx.o(.text)
 <LI><a href="#[13]">TAMP_STAMP_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[29]">TIM1_BRK_TIM15_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[2c]">TIM1_CC_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[2b]">TIM1_TRG_COM_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[2a]">TIM1_UP_TIM16_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[2d]">TIM2_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[36]">TIM6_DAC_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[37]">TIM7_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[49]">TSC_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[31]">USART1_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[32]">USART2_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[40]">USB_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[c]">UsageFault_Handler</a> from stm32l4xx_it.o(i.UsageFault_Handler) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[11]">WWDG_IRQHandler</a> from startup_stm32l432xx.o(.text) referenced from startup_stm32l432xx.o(RESET)
 <LI><a href="#[51]">__main</a> from __main.o(!!!main) referenced from startup_stm32l432xx.o(.text)
 <LI><a href="#[50]">_printf_input_char</a> from _printf_char_common.o(.text) referenced from _printf_char_common.o(.text)
 <LI><a href="#[4f]">_sputc</a> from _sputc.o(.text) referenced from noretval__2sprintf.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[51]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[52]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[54]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[b0]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[b1]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[55]"></a>__scatterload_copy</STRONG> (Thumb, 26 bytes, Stack size unknown bytes, __scatter_copy.o(!!handler_copy), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>

<P><STRONG><a name="[b2]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[56]"></a>_printf_d</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_d.o(.ARM.Collect$$_printf_percent$$00000009))
<BR><BR>[Stack]<UL><LI>Max Depth = 56 + Unknown Stack Size
<LI>Call Chain = _printf_d &rArr; _printf_int_dec &rArr; _printf_int_common
</UL>
<BR>[Calls]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[6e]"></a>_printf_percent</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[b3]"></a>_printf_percent_end</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017))

<P><STRONG><a name="[5f]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[58]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000001))
<BR><BR>[Calls]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_init
</UL>

<P><STRONG><a name="[5a]"></a>__rt_lib_init_heap_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000005))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + Unknown Stack Size
<LI>Call Chain = __rt_lib_init_heap_2 &rArr; _init_alloc &rArr; __rt_SIGRTMEM &rArr; __rt_SIGRTMEM_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_init_alloc
</UL>

<P><STRONG><a name="[b4]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[b5]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[b6]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[b7]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[b8]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[b9]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[ba]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[bb]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[bc]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[bd]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[be]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[bf]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[c0]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[c1]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[c2]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[c3]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[c4]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[c5]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[c6]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[c7]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[64]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[c8]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[c9]"></a>__rt_lib_shutdown_fini_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[ca]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000009))

<P><STRONG><a name="[cb]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000011))

<P><STRONG><a name="[cc]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000012))

<P><STRONG><a name="[cd]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[ce]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000006))

<P><STRONG><a name="[cf]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E))

<P><STRONG><a name="[53]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[d0]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[5c]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[5e]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[d1]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[60]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 232 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; ssd1306_Init &rArr; ssd1306_UpdateScreen &rArr; ssd1306_WriteData &rArr; SW_I2C_WriteData &rArr; SW_I2C_WriteByte &rArr; SW_I2C_SDA_Output &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[d2]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[7e]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[63]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[d3]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[65]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[7]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[d4]"></a>_maybe_terminate_alloc</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, maybetermalloc1.o(.emb_text), UNUSED)

<P><STRONG><a name="[23]"></a>ADC1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>COMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>CRS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>DMA2_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>DMA2_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>DMA2_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>DMA2_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>DMA2_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>DMA2_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>DMA2_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>LPTIM1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>LPTIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>LPUART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>PVD_PVM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>QUADSPI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>SAI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>SWPMI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIM1_BRK_TIM15_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>TIM1_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>TIM1_UP_TIM16_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>TSC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>USB_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l432xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[7d]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_stm32l432xx.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[67]"></a>malloc</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, h1_alloc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = malloc &rArr; __Heap_Full &rArr; __Heap_ProvideMemory &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_Full
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_heap_descriptor
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_WriteData
</UL>

<P><STRONG><a name="[6a]"></a>free</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, h1_free.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = free
</UL>
<BR>[Calls]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_heap_descriptor
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_WriteData
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_ProvideMemory
</UL>

<P><STRONG><a name="[6b]"></a>__2sprintf</STRONG> (Thumb, 34 bytes, Stack size 32 bytes, noretval__2sprintf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 120 + Unknown Stack Size
<LI>Call Chain = __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6d]"></a>__printf</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, __printf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24 + Unknown Stack Size
<LI>Call Chain = __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_percent
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>

<P><STRONG><a name="[57]"></a>_printf_int_dec</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, _printf_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _printf_int_dec &rArr; _printf_int_common
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_d
</UL>

<P><STRONG><a name="[af]"></a>__aeabi_memcpy</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_WriteData
</UL>

<P><STRONG><a name="[70]"></a>__rt_memcpy</STRONG> (Thumb, 138 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>

<P><STRONG><a name="[d5]"></a>_memcpy_lastbytes</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_v6.o(.text), UNUSED)

<P><STRONG><a name="[72]"></a>__aeabi_memset</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, aeabi_memset.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_Fill
</UL>

<P><STRONG><a name="[82]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_WriteByte
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_SDA_Output
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_Init
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Read
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[d6]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[d7]"></a>__rt_memclr_w</STRONG> (Thumb, 78 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[79]"></a>_memset_w</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memclr
</UL>

<P><STRONG><a name="[d8]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[1]"></a>__rt_heap_escrow</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[0]"></a>__rt_heap_expand</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[68]"></a>__rt_heap_descriptor</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_heap_descriptor_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_init_alloc
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
</UL>

<P><STRONG><a name="[d9]"></a>__use_no_heap</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, hguard.o(.text), UNUSED)

<P><STRONG><a name="[da]"></a>__heap$guard</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, hguard.o(.text), UNUSED)

<P><STRONG><a name="[5]"></a>_terminate_user_alloc</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, init_alloc.o(.text), UNUSED)

<P><STRONG><a name="[3]"></a>_init_user_alloc</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, init_alloc.o(.text), UNUSED)

<P><STRONG><a name="[69]"></a>__Heap_Full</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, init_alloc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __Heap_Full &rArr; __Heap_ProvideMemory &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_ProvideMemory
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
</UL>

<P><STRONG><a name="[75]"></a>__Heap_Broken</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, init_alloc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM
</UL>

<P><STRONG><a name="[5b]"></a>_init_alloc</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, init_alloc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = _init_alloc &rArr; __rt_SIGRTMEM &rArr; __rt_SIGRTMEM_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_ProvideMemory
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_Initialize
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_heap_descriptor
</UL>
<BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_heap_2
</UL>

<P><STRONG><a name="[77]"></a>__Heap_Initialize</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, h1_init.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_init_alloc
</UL>

<P><STRONG><a name="[2]"></a>__Heap_DescSize</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, h1_init.o(.text), UNUSED)

<P><STRONG><a name="[6f]"></a>_printf_int_common</STRONG> (Thumb, 178 bytes, Stack size 32 bytes, _printf_intcommon.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[6c]"></a>_printf_char_common</STRONG> (Thumb, 32 bytes, Stack size 64 bytes, _printf_char_common.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>

<P><STRONG><a name="[4f]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _sputc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> noretval__2sprintf.o(.text)
</UL>
<P><STRONG><a name="[71]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memcpy
</UL>

<P><STRONG><a name="[db]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[dc]"></a>__rt_memcpy_w</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[dd]"></a>_memcpy_lastbytes_aligned</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[de]"></a>__aeabi_memclr</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)

<P><STRONG><a name="[78]"></a>__rt_memclr</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset_w
</UL>

<P><STRONG><a name="[73]"></a>_memset</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[df]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[7c]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[e0]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[74]"></a>__Heap_ProvideMemory</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, h1_extend.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __Heap_ProvideMemory &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_init_alloc
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_Full
</UL>

<P><STRONG><a name="[76]"></a>__rt_SIGRTMEM</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, defsig_rtmem_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __rt_SIGRTMEM &rArr; __rt_SIGRTMEM_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM_inner
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__sig_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_init_alloc
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_Broken
</UL>

<P><STRONG><a name="[e1]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[e2]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[e3]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[5d]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[62]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[7b]"></a>__sig_exit</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, defsig_exit.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM
</UL>

<P><STRONG><a name="[7a]"></a>__rt_SIGRTMEM_inner</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, defsig_rtmem_inner.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __rt_SIGRTMEM_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__default_signal_display
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM
</UL>

<P><STRONG><a name="[66]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__sig_exit
</UL>

<P><STRONG><a name="[7f]"></a>__default_signal_display</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, defsig_general.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ttywrch
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM_inner
</UL>

<P><STRONG><a name="[80]"></a>_ttywrch</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, sys_wrch.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _ttywrch
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__default_signal_display
</UL>

<P><STRONG><a name="[b]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_it.o(i.BusFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[81]"></a>DHT11_Read</STRONG> (Thumb, 370 bytes, Stack size 64 bytes, dht11.o(i.DHT11_Read))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = DHT11_Read &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_DelayUs
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[85]"></a>HAL_Delay</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, stm32l4xx_hal.o(i.HAL_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_Init
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Read
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[83]"></a>HAL_GPIO_Init</STRONG> (Thumb, 488 bytes, Stack size 56 bytes, stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_WriteByte
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_SDA_Output
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_Init
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Read
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[87]"></a>HAL_GPIO_ReadPin</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32l4xx_hal_gpio.o(i.HAL_GPIO_ReadPin))
<BR><BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_WriteByte
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Read
</UL>

<P><STRONG><a name="[84]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin))
<BR><BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_SDA_Low
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_SDA_High
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_SCL_Low
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_SCL_High
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Read
</UL>

<P><STRONG><a name="[88]"></a>HAL_GetTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32l4xx_hal.o(i.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>

<P><STRONG><a name="[a1]"></a>HAL_IncTick</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32l4xx_hal.o(i.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[89]"></a>HAL_Init</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, stm32l4xx_hal.o(i.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8b]"></a>HAL_InitTick</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, stm32l4xx_hal.o(i.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[8c]"></a>HAL_MspInit</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32l4xx_hal_msp.o(i.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[8f]"></a>HAL_NVIC_EnableIRQ</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32l4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
</UL>

<P><STRONG><a name="[8e]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[8a]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping))
<BR><BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[a3]"></a>HAL_PWREx_ControlVoltageScaling</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling))
<BR><BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[95]"></a>HAL_PWREx_GetVoltageRange</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange))
<BR><BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_SetFlashLatencyFromMSIRange
</UL>

<P><STRONG><a name="[91]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 304 bytes, Stack size 32 bytes, stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[92]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 150 bytes, Stack size 16 bytes, stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_RCC_GetSysClockFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>

<P><STRONG><a name="[93]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 1366 bytes, Stack size 32 bytes, stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_RCC_OscConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_SetFlashLatencyFromMSIRange
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[8d]"></a>HAL_SYSTICK_Config</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_SYSTICK_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[9]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_it.o(i.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_it.o(i.MemManage_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_it.o(i.NMI_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>RCC_IRQHandler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_it.o(i.RCC_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[96]"></a>SW_I2C_Init</STRONG> (Thumb, 78 bytes, Stack size 32 bytes, sw_i2c.o(i.SW_I2C_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = SW_I2C_Init &rArr; SW_I2C_SDA_Output &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_SDA_Output
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_SDA_High
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_SCL_High
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_DelayUs
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9d]"></a>SW_I2C_Start</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, sw_i2c.o(i.SW_I2C_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = SW_I2C_Start &rArr; SW_I2C_SDA_Output &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_SDA_Output
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_SDA_Low
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_SDA_High
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_SCL_Low
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_SCL_High
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_DelayUs
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_WriteData
</UL>

<P><STRONG><a name="[9e]"></a>SW_I2C_Stop</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, sw_i2c.o(i.SW_I2C_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = SW_I2C_Stop &rArr; SW_I2C_SDA_Output &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_SDA_Output
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_SDA_Low
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_SDA_High
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_SCL_Low
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_SCL_High
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_DelayUs
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_WriteData
</UL>

<P><STRONG><a name="[9f]"></a>SW_I2C_WriteByte</STRONG> (Thumb, 154 bytes, Stack size 32 bytes, sw_i2c.o(i.SW_I2C_WriteByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = SW_I2C_WriteByte &rArr; SW_I2C_SDA_Output &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_SDA_Output
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_SDA_Low
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_SDA_High
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_SCL_Low
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_SCL_High
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_DelayUs
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_WriteData
</UL>

<P><STRONG><a name="[a0]"></a>SW_I2C_WriteData</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, sw_i2c.o(i.SW_I2C_WriteData))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = SW_I2C_WriteData &rArr; SW_I2C_WriteByte &rArr; SW_I2C_SDA_Output &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_WriteByte
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_Stop
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_WriteData
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_WriteCommand
</UL>

<P><STRONG><a name="[10]"></a>SysTick_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32l4xx_it.o(i.SysTick_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[a2]"></a>SystemClock_Config</STRONG> (Thumb, 96 bytes, Stack size 96 bytes, main.o(i.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = SystemClock_Config &rArr; HAL_RCC_OscConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWREx_ControlVoltageScaling
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[4d]"></a>SystemInit</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, system_stm32l4xx.o(i.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(.text)
</UL>
<P><STRONG><a name="[c]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_it.o(i.UsageFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l432xx.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>main</STRONG> (Thumb, 180 bytes, Stack size 64 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 232 + Unknown Stack Size
<LI>Call Chain = main &rArr; ssd1306_Init &rArr; ssd1306_UpdateScreen &rArr; ssd1306_WriteData &rArr; SW_I2C_WriteData &rArr; SW_I2C_WriteByte &rArr; SW_I2C_SDA_Output &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_WriteString
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_UpdateScreen
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_SetCursor
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_Init
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_Fill
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_Init
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Read
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[ae]"></a>ssd1306_DrawPixel</STRONG> (Thumb, 42 bytes, Stack size 12 bytes, ssd1306.o(i.ssd1306_DrawPixel))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = ssd1306_DrawPixel
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_WriteChar
</UL>

<P><STRONG><a name="[a5]"></a>ssd1306_Fill</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, ssd1306.o(i.ssd1306_Fill))
<BR><BR>[Calls]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_Init
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a4]"></a>ssd1306_Init</STRONG> (Thumb, 194 bytes, Stack size 8 bytes, ssd1306.o(i.ssd1306_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 168 + Unknown Stack Size
<LI>Call Chain = ssd1306_Init &rArr; ssd1306_UpdateScreen &rArr; ssd1306_WriteData &rArr; SW_I2C_WriteData &rArr; SW_I2C_WriteByte &rArr; SW_I2C_SDA_Output &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_WriteCommand
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_SetDisplayOn
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_SetContrast
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_UpdateScreen
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_Fill
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ab]"></a>ssd1306_SetContrast</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ssd1306.o(i.ssd1306_SetContrast))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = ssd1306_SetContrast &rArr; ssd1306_WriteCommand &rArr; SW_I2C_WriteData &rArr; SW_I2C_WriteByte &rArr; SW_I2C_SDA_Output &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_WriteCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_Init
</UL>

<P><STRONG><a name="[a6]"></a>ssd1306_SetCursor</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, ssd1306.o(i.ssd1306_SetCursor))
<BR><BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a9]"></a>ssd1306_SetDisplayOn</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, ssd1306.o(i.ssd1306_SetDisplayOn))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = ssd1306_SetDisplayOn &rArr; ssd1306_WriteCommand &rArr; SW_I2C_WriteData &rArr; SW_I2C_WriteByte &rArr; SW_I2C_SDA_Output &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_WriteCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_Init
</UL>

<P><STRONG><a name="[a8]"></a>ssd1306_UpdateScreen</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, ssd1306.o(i.ssd1306_UpdateScreen))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = ssd1306_UpdateScreen &rArr; ssd1306_WriteData &rArr; SW_I2C_WriteData &rArr; SW_I2C_WriteByte &rArr; SW_I2C_SDA_Output &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_WriteData
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_WriteCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_Init
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ad]"></a>ssd1306_WriteChar</STRONG> (Thumb, 162 bytes, Stack size 52 bytes, ssd1306.o(i.ssd1306_WriteChar))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = ssd1306_WriteChar &rArr; ssd1306_DrawPixel
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_DrawPixel
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_WriteString
</UL>

<P><STRONG><a name="[aa]"></a>ssd1306_WriteCommand</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, ssd1306.o(i.ssd1306_WriteCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = ssd1306_WriteCommand &rArr; SW_I2C_WriteData &rArr; SW_I2C_WriteByte &rArr; SW_I2C_SDA_Output &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_WriteData
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_SetDisplayOn
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_SetContrast
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_UpdateScreen
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_Init
</UL>

<P><STRONG><a name="[ac]"></a>ssd1306_WriteData</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, ssd1306.o(i.ssd1306_WriteData))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = ssd1306_WriteData &rArr; SW_I2C_WriteData &rArr; SW_I2C_WriteByte &rArr; SW_I2C_SDA_Output &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_WriteData
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_UpdateScreen
</UL>

<P><STRONG><a name="[a7]"></a>ssd1306_WriteString</STRONG> (Thumb, 52 bytes, Stack size 28 bytes, ssd1306.o(i.ssd1306_WriteString))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = ssd1306_WriteString &rArr; ssd1306_WriteChar &rArr; ssd1306_DrawPixel
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_WriteChar
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[59]"></a>_fp_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fpinit.o(x$fpl$fpinit))
<BR><BR>[Called By]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_fp_1
</UL>

<P><STRONG><a name="[e4]"></a>__fplib_config_fpu_vfp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[e5]"></a>__fplib_config_pureend_doubles</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[86]"></a>DHT11_DelayUs</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, dht11.o(i.DHT11_DelayUs))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DHT11_DelayUs
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Read
</UL>

<P><STRONG><a name="[9a]"></a>SW_I2C_DelayUs</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, sw_i2c.o(i.SW_I2C_DelayUs))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SW_I2C_DelayUs
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_WriteByte
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_Stop
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_Start
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_Init
</UL>

<P><STRONG><a name="[98]"></a>SW_I2C_SCL_High</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, sw_i2c.o(i.SW_I2C_SCL_High))
<BR><BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_WriteByte
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_Stop
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_Start
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_Init
</UL>

<P><STRONG><a name="[9b]"></a>SW_I2C_SCL_Low</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, sw_i2c.o(i.SW_I2C_SCL_Low))
<BR><BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_WriteByte
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_Stop
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_Start
</UL>

<P><STRONG><a name="[99]"></a>SW_I2C_SDA_High</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, sw_i2c.o(i.SW_I2C_SDA_High))
<BR><BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_WriteByte
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_Stop
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_Start
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_Init
</UL>

<P><STRONG><a name="[9c]"></a>SW_I2C_SDA_Low</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, sw_i2c.o(i.SW_I2C_SDA_Low))
<BR><BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_WriteByte
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_Stop
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_Start
</UL>

<P><STRONG><a name="[97]"></a>SW_I2C_SDA_Output</STRONG> (Thumb, 42 bytes, Stack size 24 bytes, sw_i2c.o(i.SW_I2C_SDA_Output))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = SW_I2C_SDA_Output &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_WriteByte
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_Stop
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_Start
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_I2C_Init
</UL>

<P><STRONG><a name="[94]"></a>RCC_SetFlashLatencyFromMSIRange</STRONG> (Thumb, 116 bytes, Stack size 24 bytes, stm32l4xx_hal_rcc.o(i.RCC_SetFlashLatencyFromMSIRange))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = RCC_SetFlashLatencyFromMSIRange
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWREx_GetVoltageRange
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
</UL>

<P><STRONG><a name="[90]"></a>__NVIC_SetPriority</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32l4xx_hal_cortex.o(i.__NVIC_SetPriority))
<BR><BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>

<P><STRONG><a name="[50]"></a>_printf_input_char</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _printf_char_common.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_common.o(.text)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
