/**
 * @file photo_display_example.c
 * @brief OLED照片显示应用示例
 */

#include "main.h"
#include "ssd1306.h"
#include "ssd1306_fonts.h"
#include "bitmap_display.h"
#include "bitmap_data.h"
#include "DHT11.h"

/**
 * @brief 启动画面显示
 */
void Display_Startup_Screen(void)
{
    // 显示公司Logo
    ssd1306_Fill(Black);
    OLED_DisplayBitmapAt(32, 16, LOGO_WIDTH, LOGO_HEIGHT, company_logo_64x32);
    ssd1306_UpdateScreen();
    HAL_Delay(2000);
    
    // 淡出效果
    OLED_FadeBitmap(company_logo_64x32, false);
    
    // 显示产品信息
    ssd1306_Fill(Black);
    ssd1306_SetCursor(10, 10);
    ssd1306_WriteString("DHT11 Monitor", Font_11x18, White);
    ssd1306_SetCursor(30, 35);
    ssd1306_WriteString("Version 1.0", Font_7x10, White);
    ssd1306_SetCursor(35, 50);
    ssd1306_WriteString("Loading...", Font_6x8, White);
    ssd1306_UpdateScreen();
    HAL_Delay(2000);
}

/**
 * @brief 温湿度界面显示（带图标）
 */
void Display_Temperature_Humidity_UI(uint8_t temp, uint8_t humi)
{
    char buf[32];
    
    ssd1306_Fill(Black);
    
    // 显示温度图标和数值
    OLED_DisplayBitmapAt(5, 5, THERMOMETER_WIDTH, THERMOMETER_HEIGHT, thermometer_16x16);
    sprintf(buf, "%d°C", temp);
    ssd1306_SetCursor(25, 8);
    ssd1306_WriteString(buf, Font_11x18, White);
    
    // 显示湿度图标和数值
    OLED_DisplayBitmapAt(5, 35, HUMIDITY_WIDTH, HUMIDITY_HEIGHT, humidity_16x16);
    sprintf(buf, "%d%%", humi);
    ssd1306_SetCursor(25, 38);
    ssd1306_WriteString(buf, Font_11x18, White);
    
    // 显示WiFi状态图标
    OLED_DisplayBitmapAt(100, 5, WIFI_WIDTH, WIFI_HEIGHT, wifi_16x16);
    
    // 显示电池状态
    OLED_DisplayBitmapAt(90, 45, BATTERY_WIDTH, BATTERY_HEIGHT, battery_24x12);
    
    ssd1306_UpdateScreen();
}

/**
 * @brief 图片轮播显示
 */
void Display_Photo_Slideshow(void)
{
    const uint8_t* photos[] = {
        startup_screen_128x64,
        company_logo_64x32,
        // 可以添加更多照片
    };
    
    const uint8_t photo_count = sizeof(photos) / sizeof(photos[0]);
    
    for (uint8_t i = 0; i < photo_count; i++) {
        // 滚动进入效果
        if (i == 0) {
            OLED_DisplayBitmap(photos[i]);
        } else if (i == 1) {
            // 居中显示较小的图片
            ssd1306_Fill(Black);
            OLED_DisplayBitmapAt(32, 16, LOGO_WIDTH, LOGO_HEIGHT, photos[i]);
            ssd1306_UpdateScreen();
        }
        
        HAL_Delay(3000);
        
        // 淡出效果
        if (i == 0) {
            OLED_FadeBitmap(photos[i], false);
        }
    }
}

/**
 * @brief 动画效果显示
 */
void Display_Animation_Demo(void)
{
    // 帧动画
    for (uint8_t frame = 0; frame < 10; frame++) {
        ssd1306_Fill(Black);
        
        // 显示不同的图标
        uint8_t icon_index = frame % animation_frame_count;
        OLED_DisplayBitmapAt(56, 24, 16, 16, animation_frames[icon_index]);
        
        ssd1306_UpdateScreen();
        HAL_Delay(200);
    }
    
    // 滚动动画
    OLED_ScrollBitmap(smiley_32x32, SCROLL_LEFT, 50);
}

/**
 * @brief 交互式照片查看器
 */
void Interactive_Photo_Viewer(void)
{
    uint8_t current_photo = 0;
    const uint8_t total_photos = 3;
    
    while (1) {
        ssd1306_Fill(Black);
        
        switch (current_photo) {
            case 0:
                OLED_DisplayBitmap(startup_screen_128x64);
                break;
            case 1:
                OLED_DisplayBitmapAt(32, 16, LOGO_WIDTH, LOGO_HEIGHT, company_logo_64x32);
                break;
            case 2:
                OLED_DisplayBitmapAt(48, 24, SMILEY_WIDTH, SMILEY_HEIGHT, smiley_32x32);
                break;
        }
        
        // 显示导航信息
        char nav_info[32];
        sprintf(nav_info, "%d/%d", current_photo + 1, total_photos);
        ssd1306_SetCursor(100, 55);
        ssd1306_WriteString(nav_info, Font_6x8, White);
        
        ssd1306_UpdateScreen();
        
        // 这里可以添加按键检测逻辑
        // 例如：检测按键切换照片
        HAL_Delay(2000);
        current_photo = (current_photo + 1) % total_photos;
        
        // 简单的退出条件（实际应用中可以用按键）
        static uint8_t view_count = 0;
        if (++view_count >= 9) break;
    }
}

/**
 * @brief 主照片显示演示
 */
void Photo_Display_Main_Demo(void)
{
    // 1. 启动画面
    Display_Startup_Screen();
    
    // 2. 温湿度监控界面
    uint8_t temp = 25, humi = 60;
    for (uint8_t i = 0; i < 3; i++) {
        Display_Temperature_Humidity_UI(temp + i, humi + i * 2);
        HAL_Delay(2000);
    }
    
    // 3. 照片轮播
    Display_Photo_Slideshow();
    
    // 4. 动画演示
    Display_Animation_Demo();
    
    // 5. 交互式查看器
    Interactive_Photo_Viewer();
}
