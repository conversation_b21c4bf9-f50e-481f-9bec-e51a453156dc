/**
 * @file animations.c
 * @brief OLED动画效果实现
 */

#include "ssd1306.h"
#include "ssd1306_fonts.h"

/**
 * @brief 文字滚动动画
 * @param text 要滚动的文字
 * @param font 字体
 * @param speed 滚动速度 (ms)
 */
void OLED_ScrollText(char* text, FontDef font, uint16_t speed)
{
    uint8_t text_width = strlen(text) * font.FontWidth;
    
    for (int16_t x = SSD1306_WIDTH; x > -text_width; x -= 2) {
        ssd1306_Fill(Black);
        ssd1306_SetCursor(x, 20);
        ssd1306_WriteString(text, font, White);
        ssd1306_UpdateScreen();
        HAL_Delay(speed);
    }
}

/**
 * @brief 弹跳球动画
 */
void OLED_BouncingBall(void)
{
    int16_t x = 10, y = 10;
    int8_t dx = 2, dy = 2;
    uint8_t radius = 3;
    
    for (uint16_t i = 0; i < 200; i++) {
        // 清屏
        ssd1306_Fill(Black);
        
        // 绘制边界
        ssd1306_DrawRectangle(0, 0, SSD1306_WIDTH - 1, SSD1306_HEIGHT - 1, White);
        
        // 绘制球
        ssd1306_FillCircle(x, y, radius, White);
        
        // 更新位置
        x += dx;
        y += dy;
        
        // 边界检测
        if (x <= radius || x >= SSD1306_WIDTH - radius - 1) {
            dx = -dx;
        }
        if (y <= radius || y >= SSD1306_HEIGHT - radius - 1) {
            dy = -dy;
        }
        
        ssd1306_UpdateScreen();
        HAL_Delay(50);
    }
}

/**
 * @brief 加载动画
 * @param x X坐标
 * @param y Y坐标
 */
void OLED_LoadingAnimation(uint8_t x, uint8_t y)
{
    const char loading_chars[] = "|/-\\";
    
    for (uint8_t i = 0; i < 20; i++) {
        ssd1306_Fill(Black);
        
        // 显示加载文字
        ssd1306_SetCursor(30, 20);
        ssd1306_WriteString("Loading", Font_7x10, White);
        
        // 显示旋转字符
        char loading_str[2] = {loading_chars[i % 4], '\0'};
        ssd1306_SetCursor(x, y);
        ssd1306_WriteString(loading_str, Font_11x18, White);
        
        // 显示进度点
        for (uint8_t j = 0; j <= (i % 4); j++) {
            ssd1306_SetCursor(85 + j * 8, 30);
            ssd1306_WriteString(".", Font_7x10, White);
        }
        
        ssd1306_UpdateScreen();
        HAL_Delay(200);
    }
}

/**
 * @brief 波形动画
 */
void OLED_WaveAnimation(void)
{
    for (uint16_t phase = 0; phase < 360; phase += 5) {
        ssd1306_Fill(Black);
        
        // 绘制正弦波
        for (uint8_t x = 0; x < SSD1306_WIDTH; x++) {
            float angle = (x + phase) * 3.14159 / 30;
            uint8_t y = 32 + 20 * sin(angle);
            ssd1306_DrawPixel(x, y, White);
        }
        
        // 绘制余弦波
        for (uint8_t x = 0; x < SSD1306_WIDTH; x++) {
            float angle = (x + phase) * 3.14159 / 30;
            uint8_t y = 32 + 15 * cos(angle);
            ssd1306_DrawPixel(x, y, White);
        }
        
        ssd1306_UpdateScreen();
        HAL_Delay(50);
    }
}

/**
 * @brief 星空动画
 */
void OLED_StarField(void)
{
    typedef struct {
        float x, y, z;
    } Star;
    
    Star stars[50];
    
    // 初始化星星
    for (uint8_t i = 0; i < 50; i++) {
        stars[i].x = (rand() % 200) - 100;
        stars[i].y = (rand() % 200) - 100;
        stars[i].z = rand() % 100 + 1;
    }
    
    for (uint16_t frame = 0; frame < 300; frame++) {
        ssd1306_Fill(Black);
        
        for (uint8_t i = 0; i < 50; i++) {
            // 更新Z坐标（向前移动）
            stars[i].z -= 2;
            
            // 如果星星移出屏幕，重新生成
            if (stars[i].z <= 0) {
                stars[i].x = (rand() % 200) - 100;
                stars[i].y = (rand() % 200) - 100;
                stars[i].z = 100;
            }
            
            // 计算屏幕坐标
            int16_t screen_x = 64 + (stars[i].x * 64) / stars[i].z;
            int16_t screen_y = 32 + (stars[i].y * 32) / stars[i].z;
            
            // 绘制星星
            if (screen_x >= 0 && screen_x < SSD1306_WIDTH && 
                screen_y >= 0 && screen_y < SSD1306_HEIGHT) {
                
                uint8_t brightness = 100 - stars[i].z;
                if (brightness > 50) {
                    ssd1306_DrawPixel(screen_x, screen_y, White);
                    if (brightness > 80) {
                        // 亮星星画十字
                        if (screen_x > 0) ssd1306_DrawPixel(screen_x - 1, screen_y, White);
                        if (screen_x < SSD1306_WIDTH - 1) ssd1306_DrawPixel(screen_x + 1, screen_y, White);
                        if (screen_y > 0) ssd1306_DrawPixel(screen_x, screen_y - 1, White);
                        if (screen_y < SSD1306_HEIGHT - 1) ssd1306_DrawPixel(screen_x, screen_y + 1, White);
                    }
                }
            }
        }
        
        ssd1306_UpdateScreen();
        HAL_Delay(50);
    }
}

/**
 * @brief 数字雨动画（类似黑客帝国）
 */
void OLED_DigitalRain(void)
{
    typedef struct {
        uint8_t x;
        uint8_t y;
        uint8_t speed;
        char character;
    } RainDrop;
    
    RainDrop drops[20];
    
    // 初始化雨滴
    for (uint8_t i = 0; i < 20; i++) {
        drops[i].x = (i * 6) % SSD1306_WIDTH;
        drops[i].y = rand() % SSD1306_HEIGHT;
        drops[i].speed = (rand() % 3) + 1;
        drops[i].character = '0' + (rand() % 10);
    }
    
    for (uint16_t frame = 0; frame < 200; frame++) {
        ssd1306_Fill(Black);
        
        for (uint8_t i = 0; i < 20; i++) {
            // 绘制字符
            char str[2] = {drops[i].character, '\0'};
            ssd1306_SetCursor(drops[i].x, drops[i].y);
            ssd1306_WriteString(str, Font_6x8, White);
            
            // 更新位置
            drops[i].y += drops[i].speed;
            
            // 如果超出屏幕，重新开始
            if (drops[i].y > SSD1306_HEIGHT) {
                drops[i].y = 0;
                drops[i].character = '0' + (rand() % 10);
            }
            
            // 随机改变字符
            if (rand() % 10 == 0) {
                drops[i].character = '0' + (rand() % 10);
            }
        }
        
        ssd1306_UpdateScreen();
        HAL_Delay(100);
    }
}
