/**
 * @file bitmap_display.h
 * @brief OLED位图显示功能头文件
 */

#ifndef __BITMAP_DISPLAY_H__
#define __BITMAP_DISPLAY_H__

#include "stm32l4xx_hal.h"
#include <stdbool.h>

// 函数声明
void OLED_DisplayBitmap(const uint8_t* bitmap);
void OLED_DisplayBitmapAt(uint8_t x, uint8_t y, uint8_t width, uint8_t height, const uint8_t* bitmap);
void OLED_DisplayBitmapTransparent(uint8_t x, uint8_t y, uint8_t width, uint8_t height, const uint8_t* bitmap);
void OLED_DisplayBitmapInverted(const uint8_t* bitmap);
void OLED_ScrollBitmap(const uint8_t* bitmap, uint8_t direction, uint16_t speed);
void OLED_FadeBitmap(const uint8_t* bitmap, bool fade_in);

// 滚动方向定义
#define SCROLL_LEFT     0
#define SCROLL_RIGHT    1
#define SCROLL_UP       2
#define SCROLL_DOWN     3

#endif // __BITMAP_DISPLAY_H__
