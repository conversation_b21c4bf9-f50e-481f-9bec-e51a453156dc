#ifndef __DHT11_H
#define __DHT11_H

#include "stm32l4xx_hal.h"

#define DHT11_PORT GPIOA
#define DHT11_PIN  GPIO_PIN_3

// 函数声明
void DHT11_Init(void);
uint8_t DHT11_Read(uint8_t *temp, uint8_t *humi);
/***********????:
????BMP??128×64?????(x,y),
x???0~127,
y?????0~7
*****************/
 
void OLED_DrawBMP(unsigned char x0, unsigned char y0,unsigned char x1, unsigned char y1,unsigned char BMP[])
{ 	
 unsigned int j=0;
 unsigned char x,y;
  
  if(y1%8==0) y=y1/8;      
  else y=y1/8+1;
	for(y=y0;y<y1;y++)
	{
		OLED_Set_Pos(x0,y);
    for(x=x0;x<x1;x++)
	    {      
	    	OLED_WR_Byte(BMP[j++],OLED_DATA);	    	
	    }
	}
} 







unsigned char bmp_1[]=

{0xFF,0xFF,0xC0,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,
0x80,0x80,0x80,0x80,0x80,0x80,0x81,0x81,0x81,0x83,0x83,0x87,0x87,0x87,0x8F,0x8F,
0x8F,0x8F,0x9F,0x9F,0x9F,0x9F,0xBF,0xBF,0xBF,0xBF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFB,
0xFD,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xBF,0xBF,0xBF,0xBF,0xBF,0x9F,0x9F,0x9F,0x9F,0x8F,0x8F,
0x8F,0x87,0x87,0x87,0x83,0x83,0x83,0x81,0x81,0x80,0x80,0x80,0x80,0x80,0x80,0x80,
0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,
0xFF,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x03,0x03,0x07,0x0F,0x0F,0x1F,
0x3F,0x3F,0x7F,0x7F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x3F,0xFF,0xFF,0xFF,0xFF,0xFE,0xFE,
0xFE,0xFE,0xFE,0xFC,0xFC,0xFC,0xFC,0xFC,0xFC,0xFC,0xFC,0xFC,0xFC,0xF8,0xF8,0xF8,
0xF8,0xF8,0xF8,0xFC,0xFC,0xFC,0xFC,0xFC,0xFC,0xFC,0xFC,0xFC,0xFE,0xFE,0xFE,0xFE,
0xFE,0xFF,0xDF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x7F,0x7F,0x3F,0x3F,0x1F,
0x1F,0x0F,0x07,0x07,0x03,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0xFF,0xFF,0x00,0x03,0x07,0x1F,0x3F,0x7F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0xFC,
0xFC,0xF8,0xF0,0xF0,0xE0,0xE0,0xC0,0xC7,0x8F,0x9F,0x9D,0x18,0x38,0x38,0x38,0x38,
0x38,0x38,0x38,0x38,0x38,0x38,0x38,0x1C,0x1C,0x1C,0x1C,0x1E,0x0F,0x0F,0x0F,0x0F,
0x07,0x07,0x07,0x07,0x0F,0x0F,0x0F,0x0F,0x1E,0x1C,0x1C,0x1C,0x1C,0x38,0x38,0x38,
0x38,0x38,0x38,0x38,0xB8,0xB8,0xB8,0xF8,0xD8,0xDC,0xFF,0xEF,0xF7,0xF8,0xF8,0xFC,
0xFE,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xBF,0xFF,0xFF,0xBF,0xFF,0xFF,0x7F,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x7F,0x3F,0x1F,0x0F,0x07,0x01,0x00,0x00,0x00,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0xE0,0x80,0x00,0x0F,0x0F,
0x0F,0x0F,0x0F,0x00,0x00,0x0F,0x0F,0xCF,0xEF,0xFF,0xFD,0x7F,0x3F,0x1F,0x0F,0x1F,
0x1F,0x1F,0x1F,0x1F,0x1F,0x1F,0x3F,0x3E,0x7F,0x7F,0xFF,0xF7,0xE3,0xE0,0xC3,0xC7,
0x87,0x87,0x8F,0x8F,0xCF,0xC7,0xE7,0xE7,0xF3,0xFE,0xFF,0x7E,0x7F,0x3F,0x3F,0x1F,
0x1F,0x0F,0x0F,0x0F,0x0E,0x03,0x0B,0x1F,0x3F,0xFF,0xFF,0xFE,0xCF,0x07,0x07,0x07,
0x03,0x00,0x80,0xC0,0xF0,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFD,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x7F,0x0F,0x00,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xBF,0xBF,0x7F,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x1F,0x07,0x01,0x00,0xC0,0xE0,
0xE0,0xF0,0xF0,0x70,0x70,0xF0,0xF0,0xE1,0xE7,0xDF,0xBF,0xFE,0xFC,0xF8,0xF8,0xF8,
0xF8,0xF8,0xF8,0xF8,0xF8,0xFC,0xFC,0xFE,0xFE,0xFF,0xFF,0xEF,0xC7,0x07,0xE3,0xE3,
0xF1,0xF1,0xF1,0xF1,0xF3,0xF3,0xE3,0xE7,0xE7,0x0F,0x0F,0x1E,0xFE,0xFC,0xFC,0xF8,
0xF8,0x00,0x00,0x00,0x00,0xC0,0xF0,0xFC,0xFE,0x7F,0x7F,0x7F,0x73,0xF0,0xE0,0xE0,
0xC0,0x00,0x01,0x03,0x0F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0xF0,0x00,
0xFF,0xFF,0x00,0xC0,0xF0,0xF8,0xFC,0xFE,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x7F,0x3F,
0x1F,0x1F,0x0F,0x07,0x07,0x03,0x03,0xE3,0xF1,0xF9,0xB8,0x1C,0x1C,0x0C,0x0C,0x0C,
0x0C,0x0C,0x0C,0x1C,0x1C,0x1C,0x1C,0x1C,0x3C,0x38,0x38,0xB8,0xF8,0xF0,0xF0,0xF0,
0xF0,0xE0,0xE0,0xF0,0xF0,0xF0,0xF0,0xF8,0xB8,0x38,0x38,0x38,0x1C,0x1C,0x1C,0x1C,
0x1C,0x0C,0x0C,0x0C,0x0C,0x0D,0x0D,0x1D,0x1F,0x1B,0xFF,0xF7,0xEF,0x0F,0x1F,0x3F,
0x7F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0xFC,0xF8,0xF0,0xE0,0x80,0x00,0x00,0x00,
0xFF,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0xC0,0xE0,0xE0,0xF0,0xF8,0xF8,
0xFC,0xFC,0xFE,0xFE,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x7F,0x7F,0x7F,
0x7F,0x7F,0x3F,0x3F,0x3F,0x3F,0x3F,0x3F,0x3F,0x1F,0x1F,0x1F,0x1F,0x1F,0x1F,0x1F,
0x1F,0x1F,0x1F,0x1F,0x1F,0x1F,0x1F,0x3F,0x3F,0x3F,0x3F,0x3F,0x3F,0x3F,0x7F,0x7F,
0x7F,0x7F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0xFE,0xFC,0xF8,
0xF8,0xF0,0xE0,0xE0,0xC0,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0xFF,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x80,0xC0,0xC0,0xC0,0xE0,0xE0,0xE0,0xF0,0xF0,
0xF0,0xF8,0xF8,0xF8,0xF8,0xFC,0xFC,0xFC,0xFC,0xFE,0xFE,0xFE,0xFE,0xFE,0xFE,0xFE,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFE,0xFE,0xFE,0xFE,0xFE,0xFE,0xFC,0xFC,0xFC,0xFC,0xFC,0xF8,0xF8,0xF8,0xF0,0xF0,
0xF0,0xF0,0xE0,0xE0,0xE0,0xC0,0xC0,0x80,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

};/*"D:\qianrushi\screenshot-20250603-161852 (1).bmp",0*/


unsigned char bmp_2[]=
{0xFF,0x00,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,
0x80,0x80,0x80,0x80,0x80,0x81,0x81,0x83,0x83,0x83,0x87,0x87,0x87,0x8F,0x8F,0x8F,
0x9F,0x9F,0x9F,0x9F,0xBF,0xBF,0xBF,0xBF,0xBF,0xFF,0x7F,0x7F,0x7F,0x7F,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0x7F,0x7F,0x7F,0x7F,0x7F,0xFF,0xBF,0xBF,0xBF,0xBF,0x9F,0x9F,0x9F,0x9F,
0x8F,0x8F,0x8F,0x8F,0x87,0x87,0x87,0x83,0x83,0x81,0x81,0x80,0x80,0x80,0x80,0x80,
0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,
0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x03,0x03,0x07,0x0F,0x1F,0x1F,0x3F,
0x3F,0x7F,0x7F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFE,0xFE,0xFC,0xFC,0xFC,0xF8,0xF8,0xF8,0xF0,0xF0,0xF0,0xE0,0xE0,0xE0,0xE0,0xC0,
0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,
0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,
0xC0,0xE0,0xE0,0xE0,0xE0,0xF0,0xF0,0xF0,0xF0,0xF8,0xF8,0xF8,0xFC,0xFC,0xFC,0xFE,
0xFE,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x7F,0x7F,0x3F,
0x3F,0x1F,0x1F,0x0F,0x0F,0x0F,0x03,0x03,0x03,0x01,0x00,0x00,0x00,0x00,0x00,0x00,
0xFF,0x03,0x03,0x0F,0x3F,0x3F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0xFC,0xF8,0xE0,0xC0,0xE0,0x80,0x80,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x80,0x80,0xC0,0xE0,0xE0,0xF8,0xF8,0xFE,0xFE,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x3F,0x1F,0x1F,0x07,0x02,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xF8,0xF0,0xC0,0x80,0x00,0x00,0x00,0x7F,0x7F,0x7F,0x7F,0x7F,0x7F,0x7F,0x7F,
0x7F,0x7F,0x7F,0x7F,0x7F,0x7F,0x7F,0x7F,0x7F,0x7F,0x00,0x60,0x60,0x7E,0x7E,0x7F,
0x67,0x07,0x43,0x67,0x67,0x7F,0x7F,0x7F,0x5F,0x1F,0x1B,0x1B,0x1F,0x1F,0x3F,0x7F,
0x7F,0x7F,0x7F,0x1F,0x1F,0x1F,0x1F,0x1E,0x1E,0x1E,0x1F,0x1F,0x1F,0x1F,0x1F,0x1F,
0x1F,0x1F,0x1B,0x1A,0x1F,0x1F,0x1F,0x1F,0x7F,0x7F,0x7F,0x7F,0x3F,0x3F,0x3F,0x1B,
0x1F,0x1F,0x1F,0x1F,0x1F,0x1F,0x1E,0x1A,0x18,0x00,0x00,0x00,0x80,0xD0,0xF2,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0x1F,0x0B,0x01,0x00,0x00,0x00,0x20,0xEE,0xFE,0xFE,0xFE,0xFE,0xFE,0xFE,0xFE,
0xFE,0xFE,0xFE,0xFE,0xFE,0xFE,0xFE,0xFE,0xEE,0x3C,0x00,0x68,0x7C,0x7E,0xFE,0xFE,
0xFE,0x7E,0x7C,0xFC,0xF8,0xF0,0xF0,0xF0,0xF8,0xF8,0x98,0x9C,0xBC,0xFC,0xF8,0xF8,
0xF8,0xFC,0xF8,0xF8,0xFC,0x7C,0x7C,0xFC,0xF8,0x78,0x78,0x78,0xFC,0xFC,0xFE,0x7E,
0x7E,0xFE,0xFE,0xFE,0x7E,0x7E,0x7C,0x7C,0xF8,0xFC,0xFC,0x78,0x78,0xF8,0x78,0xF8,
0xF8,0xF8,0xF8,0xF8,0xF8,0xF8,0xF8,0xF0,0x60,0x00,0x00,0x00,0x03,0x09,0x4F,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xC0,0xC0,0xF8,0xFC,0xFC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0x7F,0x3F,0x1F,0x0F,0x0F,0x07,0x03,0x03,0x01,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x01,0x03,0x03,0x03,0x03,0x03,0x03,0x03,0x03,0x03,0x03,0x03,0x03,
0x03,0x00,0x03,0x03,0x03,0x03,0x03,0x01,0x03,0x03,0x03,0x03,0x03,0x03,0x03,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x01,0x01,0x03,0x07,0x07,0x0F,0x3F,0x7F,0x7F,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0xFC,0xF8,0xF8,0xC0,0x40,
0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0xC0,0xC0,0xE0,0xF0,0xF0,0xF8,0xF8,0xFE,
0xFE,0xFE,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x7F,
0x7F,0x3F,0x3F,0x3F,0x1F,0x1F,0x1F,0x0F,0x0F,0x0F,0x07,0x07,0x07,0x07,0x03,0x03,
0x03,0x33,0x73,0xF3,0xF3,0xF1,0xF1,0xF1,0xF1,0xF1,0xF1,0xF1,0xF1,0xF1,0xF1,0xE1,
0xF1,0xF1,0xF1,0xF1,0xF1,0xF1,0x71,0x71,0xF1,0xF1,0xF1,0xF3,0xE3,0xE3,0xE3,0x03,
0x03,0x03,0x07,0x07,0x07,0x07,0x0F,0x0F,0x0F,0x0F,0x1F,0x1F,0x1F,0x3F,0x3F,0x7F,
0x7F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0xFE,
0xFC,0xFC,0xFC,0xF0,0xF0,0xF0,0xC0,0xC0,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0xFF,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,
0x01,0x01,0x01,0x01,0x81,0x81,0x81,0xC1,0xC1,0xE1,0xE1,0xE1,0xF1,0xF1,0xF1,0xF9,
0xF9,0xF9,0xF9,0xFD,0xFD,0xFD,0xFD,0xFF,0xFF,0xFF,0xFE,0xFE,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0xFE,0xFF,0xFF,0xFD,0xFD,0xFD,0xFD,0xF9,0xF9,0xF9,
0xF9,0xF1,0xF1,0xF1,0xF1,0xE1,0xE1,0xC1,0xC1,0xC1,0x81,0x81,0x01,0x01,0x01,0x01,
0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01};/*"D:\qianrushi\screenshot-20250603-162021 (1).bmp",0*/



// 错误代码说明
// 0: 成功
// 1: 等待DHT11响应超时（DHT11可能未连接）
// 2: DHT11响应信号异常
// 3: DHT11响应结束信号异常
// 4: 数据位读取超时
// 5: 数据位结束超时
// 6: 校验和错误

#endif
