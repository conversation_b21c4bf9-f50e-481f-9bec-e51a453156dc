/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "DHT11.h"
#include "ssd1306.h"
#include "ssd1306_fonts.h"
#include "bmp_images.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
I2C_HandleTypeDef hi2c1;

/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
static void MX_GPIO_Init(void);
static void MX_I2C1_Init(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{
  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_I2C1_Init();
  /* USER CODE BEGIN 2 */

  /* USER CODE END 2 */

  /* DWT微秒延时初始化 */
  CoreDebug->DEMCR |= CoreDebug_DEMCR_TRCENA_Msk;
  DWT->CYCCNT = 0;
  DWT->CTRL |= DWT_CTRL_CYCCNTENA_Msk;

  // 初始化OLED显示
  ssd1306_Init();

  // 显示启动Logo和图标
  ssd1306_Fill(Black);

  // 显示启动Logo (居中显示)
  ssd1306_DrawBMPBuffer(32, 0, startup_logo_64x32, LOGO_WIDTH, LOGO_HEIGHT);
  ssd1306_UpdateScreen();
  HAL_Delay(2000);

  // 显示图标演示页面
  ssd1306_Fill(Black);
  ssd1306_SetCursor(20, 0);
  ssd1306_WriteString("Icons Demo", Font_11x18, White);

  // 显示温度图标
  ssd1306_DrawBMPBuffer(10, 25, temperature_icon_16x16, TEMP_ICON_WIDTH, TEMP_ICON_HEIGHT);
  ssd1306_SetCursor(30, 30);
  ssd1306_WriteString("Temp", Font_6x8, White);

  // 显示湿度图标
  ssd1306_DrawBMPBuffer(70, 25, humidity_icon_16x16, HUMIDITY_ICON_WIDTH, HUMIDITY_ICON_HEIGHT);
  ssd1306_SetCursor(90, 30);
  ssd1306_WriteString("Humi", Font_6x8, White);

  // 显示笑脸图标
  ssd1306_DrawBMPBuffer(10, 45, smiley_face_16x16, SMILEY_WIDTH, SMILEY_HEIGHT);
  ssd1306_SetCursor(30, 50);
  ssd1306_WriteString("Happy", Font_6x8, White);

  // 显示心形图标
  ssd1306_DrawBMPBuffer(70, 45, heart_icon_16x16, HEART_WIDTH, HEART_HEIGHT);
  ssd1306_SetCursor(90, 50);
  ssd1306_WriteString("Love", Font_6x8, White);

  ssd1306_UpdateScreen();
  HAL_Delay(3000);

  // 初始化DHT11
  ssd1306_Fill(Black);
  ssd1306_SetCursor(2, 20);
  ssd1306_WriteString("DHT11 Init...", Font_11x18, White);
  ssd1306_UpdateScreen();
  DHT11_Init();

  // 显示初始化完成
  ssd1306_Fill(Black);
  ssd1306_SetCursor(2, 0);
  ssd1306_WriteString("Init Done", Font_11x18, White);
  ssd1306_SetCursor(2, 20);
  ssd1306_WriteString("Starting...", Font_7x10, White);
  ssd1306_UpdateScreen();
  HAL_Delay(1000);

  uint8_t temp, humi;
  char buf[32];
  uint8_t error_count = 0;

  while (1)
  {
    uint8_t result = DHT11_Read(&temp, &humi);

    ssd1306_Fill(Black);

    if (result == 0) {
      // 成功读取
      // 显示温度图标和数值
      ssd1306_DrawBMPBuffer(2, 0, temperature_icon_16x16, TEMP_ICON_WIDTH, TEMP_ICON_HEIGHT);
      sprintf(buf, "T:%dC", temp);
      ssd1306_SetCursor(22, 5);
      ssd1306_WriteString(buf, Font_11x18, White);

      // 显示湿度图标和数值
      ssd1306_DrawBMPBuffer(70, 0, humidity_icon_16x16, HUMIDITY_ICON_WIDTH, HUMIDITY_ICON_HEIGHT);
      sprintf(buf, "H:%d%%", humi);
      ssd1306_SetCursor(90, 5);
      ssd1306_WriteString(buf, Font_11x18, White);

      // 显示状态
      ssd1306_SetCursor(2, 25);
      ssd1306_WriteString("Status: OK", Font_7x10, White);

      // 显示笑脸图标表示正常
      ssd1306_DrawBMPBuffer(100, 25, smiley_face_16x16, SMILEY_WIDTH, SMILEY_HEIGHT);

      error_count = 0; // 重置错误计数
    } else {
      // 读取失败
      error_count++;
      sprintf(buf, "DHT11 Err:%d", result);
      ssd1306_SetCursor(2, 0);
      ssd1306_WriteString(buf, Font_11x18, White);

      // 显示错误含义
      const char* error_msg[] = {
        "OK", "No Response", "Signal Err", "End Err",
        "Data Timeout", "Bit Timeout", "Checksum Err"
      };
      if (result <= 6) {
        ssd1306_SetCursor(2, 25);
        ssd1306_WriteString((char*)error_msg[result], Font_7x10, White);
      }

      sprintf(buf, "Count:%d", error_count);
      ssd1306_SetCursor(2, 40);
      ssd1306_WriteString(buf, Font_6x8, White);
    }

    // 显示系统时钟信息
    sprintf(buf, "CLK:%luMHz", SystemCoreClock/1000000);
    ssd1306_SetCursor(2, 55);
    ssd1306_WriteString(buf, Font_6x8, White);

    ssd1306_UpdateScreen();
    HAL_Delay(2000); // 2秒刷新一次
  }
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  if (HAL_PWREx_ControlVoltageScaling(PWR_REGULATOR_VOLTAGE_SCALE1) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_MSI;
  RCC_OscInitStruct.MSIState = RCC_MSI_ON;
  RCC_OscInitStruct.MSICalibrationValue = 0;
  RCC_OscInitStruct.MSIClockRange = RCC_MSIRANGE_11; // 48MHz
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_NONE;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_MSI;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2) != HAL_OK)
  {
    Error_Handler();
  }
}

/**
  * @brief GPIO Initialization Function
  * @param None
  * @retval None
  */
static void MX_GPIO_Init(void)
{
  GPIO_InitTypeDef GPIO_InitStruct = {0};
/* USER CODE BEGIN MX_GPIO_Init_1 */
/* USER CODE END MX_GPIO_Init_1 */

  /* GPIO Ports Clock Enable */
  __HAL_RCC_GPIOA_CLK_ENABLE();
  __HAL_RCC_GPIOB_CLK_ENABLE();

  /*Configure GPIO pin : PA3 (DHT11) */
  GPIO_InitStruct.Pin = GPIO_PIN_3;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD; // 开漏输出，支持双向通信
  GPIO_InitStruct.Pull = GPIO_PULLUP; // 内部上拉
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
  HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

  // 初始状态设为高电平
  HAL_GPIO_WritePin(GPIOA, GPIO_PIN_3, GPIO_PIN_SET);

/* USER CODE BEGIN MX_GPIO_Init_2 */
/* USER CODE END MX_GPIO_Init_2 */
}

/**
  * @brief I2C1 Initialization Function
  * @param None
  * @retval None
  */
static void MX_I2C1_Init(void)
{
  /* USER CODE BEGIN I2C1_Init 0 */

  /* USER CODE END I2C1_Init 0 */

  /* USER CODE BEGIN I2C1_Init 1 */

  /* USER CODE END I2C1_Init 1 */
  hi2c1.Instance = I2C1;
  hi2c1.Init.Timing = 0x00B03FDB; // 100kHz @ 48MHz
  hi2c1.Init.OwnAddress1 = 0;
  hi2c1.Init.AddressingMode = I2C_ADDRESSINGMODE_7BIT;
  hi2c1.Init.DualAddressMode = I2C_DUALADDRESS_DISABLE;
  hi2c1.Init.OwnAddress2 = 0;
  hi2c1.Init.OwnAddress2Masks = I2C_OA2_NOMASK;
  hi2c1.Init.GeneralCallMode = I2C_GENERALCALL_DISABLE;
  hi2c1.Init.NoStretchMode = I2C_NOSTRETCH_DISABLE;
  if (HAL_I2C_Init(&hi2c1) != HAL_OK)
  {
    Error_Handler();
  }

  /** Configure Analogue filter
  */
  if (HAL_I2CEx_ConfigAnalogFilter(&hi2c1, I2C_ANALOGFILTER_ENABLE) != HAL_OK)
  {
    Error_Handler();
  }

  /** Configure Digital filter
  */
  if (HAL_I2CEx_ConfigDigitalFilter(&hi2c1, 0) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN I2C1_Init 2 */

  /* USER CODE END I2C1_Init 2 */
}

/* USER CODE BEGIN 4 */

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
