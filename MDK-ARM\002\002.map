Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32l432xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32l432xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32l432xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32l432xx.o(RESET) refers to startup_stm32l432xx.o(STACK) for __initial_sp
    startup_stm32l432xx.o(RESET) refers to startup_stm32l432xx.o(.text) for Reset_Handler
    startup_stm32l432xx.o(RESET) refers to stm32l4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32l432xx.o(RESET) refers to stm32l4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32l432xx.o(RESET) refers to stm32l4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32l432xx.o(RESET) refers to stm32l4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32l432xx.o(RESET) refers to stm32l4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32l432xx.o(RESET) refers to stm32l4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32l432xx.o(RESET) refers to stm32l4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32l432xx.o(RESET) refers to stm32l4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32l432xx.o(RESET) refers to stm32l4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32l432xx.o(RESET) refers to stm32l4xx_it.o(i.RCC_IRQHandler) for RCC_IRQHandler
    startup_stm32l432xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32l432xx.o(.text) refers to system_stm32l4xx.o(i.SystemInit) for SystemInit
    startup_stm32l432xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32l432xx.o(.text) refers to startup_stm32l432xx.o(HEAP) for Heap_Mem
    startup_stm32l432xx.o(.text) refers to startup_stm32l432xx.o(STACK) for Stack_Mem
    main.o(i.MX_I2C1_Init) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    main.o(i.MX_I2C1_Init) refers to stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter) for HAL_I2CEx_ConfigAnalogFilter
    main.o(i.MX_I2C1_Init) refers to stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter) for HAL_I2CEx_ConfigDigitalFilter
    main.o(i.MX_I2C1_Init) refers to main.o(.bss) for .bss
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) for HAL_PWREx_ControlVoltageScaling
    main.o(i.SystemClock_Config) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.main) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(i.main) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(i.main) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    main.o(i.main) refers to stm32l4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.main) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    main.o(i.main) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(i.main) refers to main.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main.o(i.main) refers to ssd1306.o(i.ssd1306_Init) for ssd1306_Init
    main.o(i.main) refers to dht11.o(i.DHT11_Init) for DHT11_Init
    main.o(i.main) refers to dht11.o(i.DHT11_Read) for DHT11_Read
    main.o(i.main) refers to ssd1306.o(i.ssd1306_Fill) for ssd1306_Fill
    main.o(i.main) refers to noretval__2sprintf.o(.text) for __2sprintf
    main.o(i.main) refers to ssd1306.o(i.ssd1306_SetCursor) for ssd1306_SetCursor
    main.o(i.main) refers to ssd1306.o(i.ssd1306_WriteString) for ssd1306_WriteString
    main.o(i.main) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    main.o(i.main) refers to ssd1306.o(i.ssd1306_UpdateScreen) for ssd1306_UpdateScreen
    main.o(i.main) refers to stm32l4xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.main) refers to ssd1306_fonts.o(.constdata) for Font_11x18
    main.o(i.main) refers to ssd1306_fonts.o(.constdata) for Font_7x10
    main.o(i.main) refers to ssd1306_fonts.o(.constdata) for Font_6x8
    main.o(i.main) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    main.o(i.main) refers to main.o(.constdata) for .constdata
    main.o(.constdata) refers to main.o(.conststring) for .conststring
    stm32l4xx_it.o(i.SysTick_Handler) refers to stm32l4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32l4xx_hal_msp.o(i.HAL_I2C_MspDeInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    stm32l4xx_hal_msp.o(i.HAL_I2C_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stm32l4xx_hal_msp.o(i.HAL_I2C_MspInit) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    stm32l4xx_hal_msp.o(i.HAL_I2C_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    stm32l4xx_hal_msp.o(i.HAL_I2C_MspInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32l4xx_hal_msp.o(i.HAL_MspInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32l4xx_hal_msp.o(i.HAL_MspInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    dht11.o(i.DHT11_DelayUs) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    dht11.o(i.DHT11_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    dht11.o(i.DHT11_Init) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dht11.o(i.DHT11_Init) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    dht11.o(i.DHT11_Init) refers to stm32l4xx_hal.o(i.HAL_Delay) for HAL_Delay
    dht11.o(i.DHT11_Read) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    dht11.o(i.DHT11_Read) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dht11.o(i.DHT11_Read) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    dht11.o(i.DHT11_Read) refers to stm32l4xx_hal.o(i.HAL_Delay) for HAL_Delay
    dht11.o(i.DHT11_Read) refers to dht11.o(i.DHT11_DelayUs) for DHT11_DelayUs
    dht11.o(i.DHT11_Read) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    ssd1306.o(i.ssd1306_DrawArc) refers to ssd1306.o(i.ssd1306_NormalizeTo0_360) for ssd1306_NormalizeTo0_360
    ssd1306.o(i.ssd1306_DrawArc) refers to ssd1306.o(i.ssd1306_DegToRad) for ssd1306_DegToRad
    ssd1306.o(i.ssd1306_DrawArc) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    ssd1306.o(i.ssd1306_DrawArc) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    ssd1306.o(i.ssd1306_DrawArc) refers to ssd1306.o(i.ssd1306_Line) for ssd1306_Line
    ssd1306.o(i.ssd1306_DrawArcWithRadiusLine) refers to ssd1306.o(i.ssd1306_NormalizeTo0_360) for ssd1306_NormalizeTo0_360
    ssd1306.o(i.ssd1306_DrawArcWithRadiusLine) refers to ssd1306.o(i.ssd1306_DegToRad) for ssd1306_DegToRad
    ssd1306.o(i.ssd1306_DrawArcWithRadiusLine) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    ssd1306.o(i.ssd1306_DrawArcWithRadiusLine) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    ssd1306.o(i.ssd1306_DrawArcWithRadiusLine) refers to ssd1306.o(i.ssd1306_Line) for ssd1306_Line
    ssd1306.o(i.ssd1306_DrawBMP) refers to ssd1306.o(i.ssd1306_SetPosition) for ssd1306_SetPosition
    ssd1306.o(i.ssd1306_DrawBMP) refers to ssd1306.o(i.ssd1306_WriteData) for ssd1306_WriteData
    ssd1306.o(i.ssd1306_DrawBMPBuffer) refers to ssd1306.o(i.ssd1306_DrawPixel) for ssd1306_DrawPixel
    ssd1306.o(i.ssd1306_DrawBitmap) refers to ssd1306.o(i.ssd1306_DrawPixel) for ssd1306_DrawPixel
    ssd1306.o(i.ssd1306_DrawCircle) refers to ssd1306.o(i.ssd1306_DrawPixel) for ssd1306_DrawPixel
    ssd1306.o(i.ssd1306_DrawPixel) refers to ssd1306.o(.bss) for .bss
    ssd1306.o(i.ssd1306_DrawRectangle) refers to ssd1306.o(i.ssd1306_Line) for ssd1306_Line
    ssd1306.o(i.ssd1306_Fill) refers to aeabi_memset.o(.text) for __aeabi_memset
    ssd1306.o(i.ssd1306_Fill) refers to ssd1306.o(.bss) for .bss
    ssd1306.o(i.ssd1306_FillBuffer) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ssd1306.o(i.ssd1306_FillBuffer) refers to ssd1306.o(.bss) for .bss
    ssd1306.o(i.ssd1306_FillCircle) refers to ssd1306.o(i.ssd1306_DrawPixel) for ssd1306_DrawPixel
    ssd1306.o(i.ssd1306_FillRectangle) refers to ssd1306.o(i.ssd1306_DrawPixel) for ssd1306_DrawPixel
    ssd1306.o(i.ssd1306_GetDisplayOn) refers to ssd1306.o(.data) for .data
    ssd1306.o(i.ssd1306_Init) refers to stm32l4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ssd1306.o(i.ssd1306_Init) refers to ssd1306.o(i.ssd1306_SetDisplayOn) for ssd1306_SetDisplayOn
    ssd1306.o(i.ssd1306_Init) refers to ssd1306.o(i.ssd1306_WriteCommand) for ssd1306_WriteCommand
    ssd1306.o(i.ssd1306_Init) refers to ssd1306.o(i.ssd1306_SetContrast) for ssd1306_SetContrast
    ssd1306.o(i.ssd1306_Init) refers to ssd1306.o(i.ssd1306_Fill) for ssd1306_Fill
    ssd1306.o(i.ssd1306_Init) refers to ssd1306.o(i.ssd1306_UpdateScreen) for ssd1306_UpdateScreen
    ssd1306.o(i.ssd1306_Init) refers to ssd1306.o(.data) for .data
    ssd1306.o(i.ssd1306_InvertRectangle) refers to ssd1306.o(.bss) for .bss
    ssd1306.o(i.ssd1306_Line) refers to ssd1306.o(i.ssd1306_DrawPixel) for ssd1306_DrawPixel
    ssd1306.o(i.ssd1306_Polyline) refers to ssd1306.o(i.ssd1306_Line) for ssd1306_Line
    ssd1306.o(i.ssd1306_SetContrast) refers to ssd1306.o(i.ssd1306_WriteCommand) for ssd1306_WriteCommand
    ssd1306.o(i.ssd1306_SetCursor) refers to ssd1306.o(.data) for .data
    ssd1306.o(i.ssd1306_SetDisplayOn) refers to ssd1306.o(i.ssd1306_WriteCommand) for ssd1306_WriteCommand
    ssd1306.o(i.ssd1306_SetDisplayOn) refers to ssd1306.o(.data) for .data
    ssd1306.o(i.ssd1306_SetPosition) refers to ssd1306.o(i.ssd1306_WriteCommand) for ssd1306_WriteCommand
    ssd1306.o(i.ssd1306_UpdateScreen) refers to ssd1306.o(i.ssd1306_WriteCommand) for ssd1306_WriteCommand
    ssd1306.o(i.ssd1306_UpdateScreen) refers to ssd1306.o(i.ssd1306_WriteData) for ssd1306_WriteData
    ssd1306.o(i.ssd1306_UpdateScreen) refers to ssd1306.o(.bss) for .bss
    ssd1306.o(i.ssd1306_WriteChar) refers to ssd1306.o(i.ssd1306_DrawPixel) for ssd1306_DrawPixel
    ssd1306.o(i.ssd1306_WriteChar) refers to ssd1306.o(.data) for .data
    ssd1306.o(i.ssd1306_WriteCommand) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    ssd1306.o(i.ssd1306_WriteCommand) refers to main.o(.bss) for hi2c1
    ssd1306.o(i.ssd1306_WriteData) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    ssd1306.o(i.ssd1306_WriteData) refers to main.o(.bss) for hi2c1
    ssd1306.o(i.ssd1306_WriteString) refers to ssd1306.o(i.ssd1306_WriteChar) for ssd1306_WriteChar
    ssd1306_fonts.o(.constdata) refers to ssd1306_fonts.o(.constdata) for Font6x8
    ssd1306_fonts.o(.constdata) refers to ssd1306_fonts.o(.constdata) for Font7x10
    ssd1306_fonts.o(.constdata) refers to ssd1306_fonts.o(.constdata) for Font11x18
    system_stm32l4xx.o(i.SystemCoreClockUpdate) refers to system_stm32l4xx.o(.constdata) for .constdata
    system_stm32l4xx.o(i.SystemCoreClockUpdate) refers to system_stm32l4xx.o(.data) for .data
    stm32l4xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to stm32l4xx_hal_msp.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32l4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32l4xx_hal_msp.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32l4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) for I2C_Mem_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) for I2C_Mem_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) for I2C_DMASlaveReceiveCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) for I2C_DMASlaveReceiveCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) for I2C_DMASlaveTransmitCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) for I2C_DMASlaveTransmitCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32l4xx_hal_i2c.o(i.I2C_TreatErrorCallback) for I2C_TreatErrorCallback
    stm32l4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32l4xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32l4xx_hal_i2c.o(i.I2C_ITAddrCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITAddrCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_i2c.o(i.I2C_TreatErrorCallback) for I2C_TreatErrorCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_IsErrorOccurred) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.I2C_IsErrorOccurred) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) for I2C_ITMasterSeqCplt
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) for I2C_ITMasterSeqCplt
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) for I2C_ITSlaveCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITAddrCplt) for I2C_ITAddrCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) for I2C_ITSlaveCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITAddrCplt) for I2C_ITAddrCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32l4xx_hal_i2c.o(i.I2C_TreatErrorCallback) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32l4xx_hal_i2c.o(i.I2C_TreatErrorCallback) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32l4xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32l4xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32l4xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32l4xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal.o(i.HAL_DeInit) refers to stm32l4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32l4xx_hal.o(i.HAL_Delay) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal.o(i.HAL_Delay) refers to stm32l4xx_hal.o(.data) for .data
    stm32l4xx_hal.o(i.HAL_GetTick) refers to stm32l4xx_hal.o(.data) for .data
    stm32l4xx_hal.o(i.HAL_GetTickFreq) refers to stm32l4xx_hal.o(.data) for .data
    stm32l4xx_hal.o(i.HAL_GetTickPrio) refers to stm32l4xx_hal.o(.data) for .data
    stm32l4xx_hal.o(i.HAL_IncTick) refers to stm32l4xx_hal.o(.data) for .data
    stm32l4xx_hal.o(i.HAL_Init) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32l4xx_hal.o(i.HAL_Init) refers to stm32l4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32l4xx_hal.o(i.HAL_Init) refers to stm32l4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32l4xx_hal.o(i.HAL_InitTick) refers to stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32l4xx_hal.o(i.HAL_InitTick) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32l4xx_hal.o(i.HAL_InitTick) refers to stm32l4xx_hal.o(.data) for .data
    stm32l4xx_hal.o(i.HAL_InitTick) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal.o(i.HAL_SetTickFreq) refers to stm32l4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32l4xx_hal.o(i.HAL_SetTickFreq) refers to stm32l4xx_hal.o(.data) for .data
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32l4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32l4xx.o(.constdata) for AHBPrescTable
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32l4xx_hal.o(.data) for uwTickPrio
    stm32l4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32l4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32l4xx_hal.o(.data) for uwTickPrio
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32l4xx.o(.constdata) for APBPrescTable
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32l4xx.o(.constdata) for APBPrescTable
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to system_stm32l4xx.o(.constdata) for MSIRangeTable
    stm32l4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32l4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l4xx_hal_rcc.o(i.RCC_SetFlashLatencyFromMSIRange) for RCC_SetFlashLatencyFromMSIRange
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32l4xx.o(.constdata) for AHBPrescTable
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l4xx_hal.o(.data) for uwTickPrio
    stm32l4xx_hal_rcc.o(i.RCC_SetFlashLatencyFromMSIRange) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange) for HAL_PWREx_GetVoltageRange
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback) for HAL_RCCEx_CRS_SyncOkCallback
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback) for HAL_RCCEx_CRS_SyncWarnCallback
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback) for HAL_RCCEx_CRS_ExpectedSyncCallback
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback) for HAL_RCCEx_CRS_ErrorCallback
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO) refers to stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO) refers to stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess) for HAL_PWR_DisableBkUpAccess
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLSAI1) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess) for HAL_PWR_DisableBkUpAccess
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLSAI1) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32l4xx_hal_rcc_ex.o(i.RCCEx_GetSAIxPeriphCLKFreq) for RCCEx_GetSAIxPeriphCLKFreq
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to system_stm32l4xx.o(.constdata) for MSIRangeTable
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback) for HAL_RCCEx_LSECSS_Callback
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32l4xx_hal_rcc_ex.o(i.RCCEx_PLLSAI1_Config) for RCCEx_PLLSAI1_Config
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc_ex.o(i.RCCEx_PLLSAI1_Config) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32l4xx_hal_flash.o(.data) for .data
    stm32l4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32l4xx_hal_flash.o(.data) for .data
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash.o(.data) for .data
    stm32l4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l4xx_hal_flash.o(i.FLASH_Program_Fast) for FLASH_Program_Fast
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l4xx_hal_flash.o(.data) for .data
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32l4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32l4xx_hal_flash.o(i.FLASH_Program_Fast) for FLASH_Program_Fast
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32l4xx_hal_flash.o(.data) for .data
    stm32l4xx_hal_flash_ex.o(i.FLASH_FlushCaches) refers to stm32l4xx_hal_flash.o(.data) for pFlash
    stm32l4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l4xx_hal_flash.o(.data) for pFlash
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32l4xx_hal_flash.o(.data) for pFlash
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig) for FLASH_OB_PCROPConfig
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l4xx_hal_flash.o(.data) for pFlash
    stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32l4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32l4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32l4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableLowPowerRunMode) for HAL_PWREx_EnableLowPowerRunMode
    stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) for HAL_PWREx_DisableLowPowerRunMode
    stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP0Mode) for HAL_PWREx_EnterSTOP0Mode
    stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP1Mode) for HAL_PWREx_EnterSTOP1Mode
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableSRAM2ContentRetention) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_SetSRAM2ContentRetention) for HAL_PWREx_SetSRAM2ContentRetention
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableSRAM2ContentRetention) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_SetSRAM2ContentRetention) for HAL_PWREx_SetSRAM2ContentRetention
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32l4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM1Callback) for HAL_PWREx_PVM1Callback
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM3Callback) for HAL_PWREx_PVM3Callback
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM4Callback) for HAL_PWREx_PVM4Callback
    stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32l4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32l4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    aeabi_memset.o(.text) refers to rt_memclr.o(.text) for _memset
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    cosf.o(i.__hardfp_cosf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf.o(i.__hardfp_cosf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf.o(i.__hardfp_cosf) refers to _rserrno.o(.text) for __set_errno
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf.o(i.__softfp_cosf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf.o(i.__softfp_cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf.o(i.cosf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf.o(i.cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf_x.o(i.____hardfp_cosf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf_x.o(i.____hardfp_cosf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf_x.o(i.____hardfp_cosf$lsc) refers to _rserrno.o(.text) for __set_errno
    cosf_x.o(i.____hardfp_cosf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf_x.o(i.____softfp_cosf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf_x.o(i.____softfp_cosf$lsc) refers to cosf_x.o(i.____hardfp_cosf$lsc) for ____hardfp_cosf$lsc
    cosf_x.o(i.__cosf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf_x.o(i.__cosf$lsc) refers to cosf_x.o(i.____hardfp_cosf$lsc) for ____hardfp_cosf$lsc
    sinf.o(i.__hardfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__hardfp_sinf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf.o(i.__hardfp_sinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    sinf.o(i.__hardfp_sinf) refers to _rserrno.o(.text) for __set_errno
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf.o(i.__softfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__softfp_sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf.o(i.sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf_x.o(i.____hardfp_sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.____hardfp_sinf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf_x.o(i.____hardfp_sinf$lsc) refers to _rserrno.o(.text) for __set_errno
    sinf_x.o(i.____hardfp_sinf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf_x.o(i.____softfp_sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.____softfp_sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sinf_x.o(i.__sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.__sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_char_common.o(.text) refers to __printf_ss.o(.text) for __printf
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(.constdata) for .constdata
    rredf.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32l432xx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_msp.o(i.HAL_I2C_MspDeInit), (56 bytes).
    Removing dht11.o(.rev16_text), (4 bytes).
    Removing dht11.o(.revsh_text), (4 bytes).
    Removing dht11.o(.rrx_text), (6 bytes).
    Removing ssd1306.o(.rev16_text), (4 bytes).
    Removing ssd1306.o(.revsh_text), (4 bytes).
    Removing ssd1306.o(.rrx_text), (6 bytes).
    Removing ssd1306.o(i.ssd1306_DegToRad), (16 bytes).
    Removing ssd1306.o(i.ssd1306_DrawArc), (312 bytes).
    Removing ssd1306.o(i.ssd1306_DrawArcWithRadiusLine), (448 bytes).
    Removing ssd1306.o(i.ssd1306_DrawBMP), (114 bytes).
    Removing ssd1306.o(i.ssd1306_DrawBMPBuffer), (140 bytes).
    Removing ssd1306.o(i.ssd1306_DrawBitmap), (124 bytes).
    Removing ssd1306.o(i.ssd1306_DrawCircle), (150 bytes).
    Removing ssd1306.o(i.ssd1306_DrawRectangle), (68 bytes).
    Removing ssd1306.o(i.ssd1306_FillBuffer), (32 bytes).
    Removing ssd1306.o(i.ssd1306_FillCircle), (148 bytes).
    Removing ssd1306.o(i.ssd1306_FillRectangle), (86 bytes).
    Removing ssd1306.o(i.ssd1306_GetDisplayOn), (12 bytes).
    Removing ssd1306.o(i.ssd1306_InvertRectangle), (152 bytes).
    Removing ssd1306.o(i.ssd1306_Line), (132 bytes).
    Removing ssd1306.o(i.ssd1306_NormalizeTo0_360), (24 bytes).
    Removing ssd1306.o(i.ssd1306_Polyline), (50 bytes).
    Removing ssd1306.o(i.ssd1306_Reset), (2 bytes).
    Removing ssd1306.o(i.ssd1306_SetPosition), (34 bytes).
    Removing ssd1306_fonts.o(.rev16_text), (4 bytes).
    Removing ssd1306_fonts.o(.revsh_text), (4 bytes).
    Removing ssd1306_fonts.o(.rrx_text), (6 bytes).
    Removing bmp_images.o(.constdata), (32 bytes).
    Removing bmp_images.o(.constdata), (32 bytes).
    Removing bmp_images.o(.constdata), (32 bytes).
    Removing bmp_images.o(.constdata), (32 bytes).
    Removing bmp_images.o(.constdata), (256 bytes).
    Removing system_stm32l4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32l4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32l4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32l4xx.o(i.SystemCoreClockUpdate), (168 bytes).
    Removing system_stm32l4xx.o(.constdata), (8 bytes).
    Removing stm32l4xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_DeInit), (50 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (56 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (104 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (16 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (40 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (266 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (128 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (298 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (304 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (132 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (342 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (164 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (406 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (230 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit), (338 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (340 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (164 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (362 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (268 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (152 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (268 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (156 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (320 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (224 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (96 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (372 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (208 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (372 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (208 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (388 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (288 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (128 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions), (26 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMAAbort), (20 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMAError), (18 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt), (94 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt), (82 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt), (36 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt), (30 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ), (94 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ), (136 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITAddrCplt), (140 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITError), (284 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt), (100 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt), (240 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITMasterSeqCplt), (76 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt), (444 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt), (114 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA), (284 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT), (324 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA), (350 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_IT), (362 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead), (100 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA), (248 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT), (280 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_TreatErrorCallback), (42 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout), (168 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_DisableFastModePlus), (40 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_DisableWakeUp), (78 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_EnableFastModePlus), (40 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_EnableWakeUp), (78 bytes).
    Removing stm32l4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DeInit), (48 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32l4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_DisableIOAnalogSwitchBooster), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_DisableMemorySwappingBank), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_EnableIOAnalogSwitchBooster), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_EnableMemorySwappingBank), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_SRAM2Erase), (28 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32l4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_DeInit), (224 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (16 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (228 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq), (36 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq), (36 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_GetResetSource), (24 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (72 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (24 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSConfig), (76 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSGetSynchronizationInfo), (36 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSSoftwareSynchronizationGenerate), (16 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization), (132 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback), (2 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler), (108 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback), (2 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback), (2 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO), (88 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSECSS), (28 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableMSIPLLMode), (16 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLSAI1), (80 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO), (140 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS), (20 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS_IT), (56 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableMSIPLLMode), (16 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLSAI1), (156 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (188 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (802 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback), (2 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler), (24 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_StandbyMSIRangeConfig), (24 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_WakeUpStopCLKConfig), (20 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.RCCEx_GetSAIxPeriphCLKFreq), (168 bytes).
    Removing stm32l4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (28 bytes).
    Removing stm32l4xx_hal_flash.o(i.FLASH_Program_Fast), (44 bytes).
    Removing stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (100 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (232 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (36 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_Program), (170 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (120 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_Unlock), (40 bytes).
    Removing stm32l4xx_hal_flash.o(.data), (32 bytes).
    Removing stm32l4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (92 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_MassErase), (28 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig), (104 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (248 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_PageErase), (40 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (222 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (140 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (148 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (278 bytes).
    Removing stm32l4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_DisableRunPowerDown), (36 bytes).
    Removing stm32l4xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_EnableRunPowerDown), (36 bytes).
    Removing stm32l4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (286 bytes).
    Removing stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32l4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32l4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32l4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_dma.o(i.DMA_SetConfig), (46 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_Abort), (74 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT), (78 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_DeInit), (156 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_IRQHandler), (186 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_Init), (192 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (218 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (74 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_Start), (80 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT), (112 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (82 bytes).
    Removing stm32l4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (128 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (20 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (72 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (36 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigPVM), (304 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBatteryCharging), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableGPIOPullDown), (76 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableGPIOPullUp), (72 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableInternalWakeUpLine), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode), (68 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM1), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM3), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM4), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePullUpPullDownConfig), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableSRAM2ContentRetention), (6 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableVddUSB), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBatteryCharging), (28 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableGPIOPullDown), (104 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableGPIOPullUp), (104 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableInternalWakeUpLine), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableLowPowerRunMode), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM1), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM3), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM4), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePullUpPullDownConfig), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableSRAM2ContentRetention), (8 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableVddUSB), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSHUTDOWNMode), (36 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP0Mode), (52 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP1Mode), (56 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP2Mode), (56 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler), (72 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM1Callback), (2 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM3Callback), (2 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM4Callback), (2 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_SetSRAM2ContentRetention), (40 bytes).
    Removing stm32l4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_MPU_Disable), (16 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_MPU_Enable), (24 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32l4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (128 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (28 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (28 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (160 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_GetPending), (32 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (44 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (176 bytes).
    Removing stm32l4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_tim_ex.o(.rrx_text), (6 bytes).

352 unused section(s) (total 24696 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32l4xx_hal_msp.c          0x00000000   Number         0  stm32l4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32l4xx_it.c               0x00000000   Number         0  stm32l4xx_it.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal.c 0x00000000   Number         0  stm32l4xx_hal.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_cortex.c 0x00000000   Number         0  stm32l4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_dma.c 0x00000000   Number         0  stm32l4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_dma_ex.c 0x00000000   Number         0  stm32l4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_exti.c 0x00000000   Number         0  stm32l4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_flash.c 0x00000000   Number         0  stm32l4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_flash_ex.c 0x00000000   Number         0  stm32l4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32l4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_gpio.c 0x00000000   Number         0  stm32l4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_i2c.c 0x00000000   Number         0  stm32l4xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32l4xx_hal_i2c_ex.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_pwr.c 0x00000000   Number         0  stm32l4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32l4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_rcc.c 0x00000000   Number         0  stm32l4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32l4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_tim.c 0x00000000   Number         0  stm32l4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_tim_ex.c 0x00000000   Number         0  stm32l4xx_hal_tim_ex.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  aeabi_memset.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf_x.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/rredf.c                       0x00000000   Number         0  rredf.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf_x.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf.o ABSOLUTE
    ..\Core\Src\DHT11.c                      0x00000000   Number         0  dht11.o ABSOLUTE
    ..\Core\Src\bmp_images.c                 0x00000000   Number         0  bmp_images.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\ssd1306.c                    0x00000000   Number         0  ssd1306.o ABSOLUTE
    ..\Core\Src\ssd1306_fonts.c              0x00000000   Number         0  ssd1306_fonts.o ABSOLUTE
    ..\Core\Src\stm32l4xx_hal_msp.c          0x00000000   Number         0  stm32l4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32l4xx_it.c               0x00000000   Number         0  stm32l4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32l4xx.c           0x00000000   Number         0  system_stm32l4xx.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal.c 0x00000000   Number         0  stm32l4xx_hal.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_cortex.c 0x00000000   Number         0  stm32l4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_dma.c 0x00000000   Number         0  stm32l4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_dma_ex.c 0x00000000   Number         0  stm32l4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_exti.c 0x00000000   Number         0  stm32l4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_flash.c 0x00000000   Number         0  stm32l4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_flash_ex.c 0x00000000   Number         0  stm32l4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32l4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_gpio.c 0x00000000   Number         0  stm32l4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_i2c.c 0x00000000   Number         0  stm32l4xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32l4xx_hal_i2c_ex.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_pwr.c 0x00000000   Number         0  stm32l4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32l4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_rcc.c 0x00000000   Number         0  stm32l4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32l4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_tim.c 0x00000000   Number         0  stm32l4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_tim_ex.c 0x00000000   Number         0  stm32l4xx_hal_tim_ex.o ABSOLUTE
    ..\\Core\\Src\\DHT11.c                   0x00000000   Number         0  dht11.o ABSOLUTE
    ..\\Core\\Src\\ssd1306.c                 0x00000000   Number         0  ssd1306.o ABSOLUTE
    ..\\Core\\Src\\ssd1306_fonts.c           0x00000000   Number         0  ssd1306_fonts.o ABSOLUTE
    ..\\Core\\Src\\system_stm32l4xx.c        0x00000000   Number         0  system_stm32l4xx.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32l432xx.s                    0x00000000   Number         0  startup_stm32l432xx.o ABSOLUTE
    RESET                                    0x08000000   Section      396  startup_stm32l432xx.o(RESET)
    !!!main                                  0x0800018c   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000194   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c8   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e4   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x08000200   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000009  0x08000200   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x08000206   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$00000017  0x0800020c   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000210   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x08000212   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000216   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000216   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000216   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000216   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x08000216   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000216   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000216   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000216   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000216   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000216   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000216   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000216   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000216   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000216   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000216   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000216   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000216   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000216   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000216   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000216   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000218   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0800021a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0800021a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x0800021a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x0800021a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x0800021a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x0800021a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x0800021a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x0800021a   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x0800021c   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0800021c   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0800021c   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000222   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000222   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000226   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000226   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800022e   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000230   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000230   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000234   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x0800023c   Section       64  startup_stm32l432xx.o(.text)
    $v0                                      0x0800023c   Number         0  startup_stm32l432xx.o(.text)
    .text                                    0x0800027c   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x080002a4   Section        0  _printf_dec.o(.text)
    .text                                    0x0800031c   Section        0  __printf_ss.o(.text)
    .text                                    0x080003d4   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000438   Section       16  aeabi_memset.o(.text)
    .text                                    0x08000448   Section       78  rt_memclr_w.o(.text)
    .text                                    0x08000496   Section        0  heapauxi.o(.text)
    .text                                    0x0800049c   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000550   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000551   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000580   Section        0  _sputc.o(.text)
    .text                                    0x0800058a   Section       68  rt_memclr.o(.text)
    .text                                    0x080005d0   Section        8  libspace.o(.text)
    .text                                    0x080005d8   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000622   Section        0  exit.o(.text)
    .text                                    0x08000634   Section        0  sys_exit.o(.text)
    .text                                    0x08000640   Section        2  use_no_semi.o(.text)
    .text                                    0x08000642   Section        0  indicate_semi.o(.text)
    i.BusFault_Handler                       0x08000642   Section        0  stm32l4xx_it.o(i.BusFault_Handler)
    i.DHT11_DelayUs                          0x08000644   Section        0  dht11.o(i.DHT11_DelayUs)
    DHT11_DelayUs                            0x08000645   Thumb Code    28  dht11.o(i.DHT11_DelayUs)
    i.DHT11_Init                             0x0800066c   Section        0  dht11.o(i.DHT11_Init)
    i.DHT11_Read                             0x080006aa   Section        0  dht11.o(i.DHT11_Read)
    i.DebugMon_Handler                       0x0800083c   Section        0  stm32l4xx_it.o(i.DebugMon_Handler)
    i.Error_Handler                          0x0800083e   Section        0  main.o(i.Error_Handler)
    i.HAL_Delay                              0x08000844   Section        0  stm32l4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x08000868   Section        0  stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x08000a6a   Section        0  stm32l4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x08000a74   Section        0  stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08000a80   Section        0  stm32l4xx_hal.o(i.HAL_GetTick)
    i.HAL_I2CEx_ConfigAnalogFilter           0x08000a8c   Section        0  stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter)
    i.HAL_I2CEx_ConfigDigitalFilter          0x08000ae2   Section        0  stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter)
    i.HAL_I2C_Init                           0x08000b34   Section        0  stm32l4xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_Mem_Write                      0x08000bf0   Section        0  stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    i.HAL_I2C_MspInit                        0x08000d50   Section        0  stm32l4xx_hal_msp.o(i.HAL_I2C_MspInit)
    i.HAL_IncTick                            0x08000dd0   Section        0  stm32l4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08000de0   Section        0  stm32l4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08000e00   Section        0  stm32l4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08000e44   Section        0  stm32l4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08000e80   Section        0  stm32l4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08000e9c   Section        0  stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08000edc   Section        0  stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_PWREx_ControlVoltageScaling        0x08000f00   Section        0  stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling)
    i.HAL_PWREx_GetVoltageRange              0x08000f68   Section        0  stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange)
    i.HAL_RCCEx_PeriphCLKConfig              0x08000f78   Section        0  stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    i.HAL_RCC_ClockConfig                    0x08001204   Section        0  stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetSysClockFreq                0x08001348   Section        0  stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x080013ec   Section        0  stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x0800194c   Section        0  stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HardFault_Handler                      0x08001974   Section        0  stm32l4xx_it.o(i.HardFault_Handler)
    i.I2C_Flush_TXDR                         0x08001976   Section        0  stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR)
    I2C_Flush_TXDR                           0x08001977   Thumb Code    34  stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR)
    i.I2C_IsErrorOccurred                    0x08001998   Section        0  stm32l4xx_hal_i2c.o(i.I2C_IsErrorOccurred)
    I2C_IsErrorOccurred                      0x08001999   Thumb Code   264  stm32l4xx_hal_i2c.o(i.I2C_IsErrorOccurred)
    i.I2C_RequestMemoryWrite                 0x08001aa4   Section        0  stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    I2C_RequestMemoryWrite                   0x08001aa5   Thumb Code    94  stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    i.I2C_TransferConfig                     0x08001b08   Section        0  stm32l4xx_hal_i2c.o(i.I2C_TransferConfig)
    I2C_TransferConfig                       0x08001b09   Thumb Code    44  stm32l4xx_hal_i2c.o(i.I2C_TransferConfig)
    i.I2C_WaitOnFlagUntilTimeout             0x08001b38   Section        0  stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    I2C_WaitOnFlagUntilTimeout               0x08001b39   Thumb Code   122  stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    i.I2C_WaitOnSTOPFlagUntilTimeout         0x08001bb2   Section        0  stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout)
    I2C_WaitOnSTOPFlagUntilTimeout           0x08001bb3   Thumb Code    86  stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout)
    i.I2C_WaitOnTXISFlagUntilTimeout         0x08001c08   Section        0  stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout)
    I2C_WaitOnTXISFlagUntilTimeout           0x08001c09   Thumb Code    90  stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout)
    i.MX_I2C1_Init                           0x08001c64   Section        0  main.o(i.MX_I2C1_Init)
    MX_I2C1_Init                             0x08001c65   Thumb Code    72  main.o(i.MX_I2C1_Init)
    i.MemManage_Handler                      0x08001cb8   Section        0  stm32l4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08001cba   Section        0  stm32l4xx_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x08001cbc   Section        0  stm32l4xx_it.o(i.PendSV_Handler)
    i.RCCEx_PLLSAI1_Config                   0x08001cc0   Section        0  stm32l4xx_hal_rcc_ex.o(i.RCCEx_PLLSAI1_Config)
    RCCEx_PLLSAI1_Config                     0x08001cc1   Thumb Code   278  stm32l4xx_hal_rcc_ex.o(i.RCCEx_PLLSAI1_Config)
    i.RCC_IRQHandler                         0x08001dec   Section        0  stm32l4xx_it.o(i.RCC_IRQHandler)
    i.RCC_SetFlashLatencyFromMSIRange        0x08001df0   Section        0  stm32l4xx_hal_rcc.o(i.RCC_SetFlashLatencyFromMSIRange)
    RCC_SetFlashLatencyFromMSIRange          0x08001df1   Thumb Code   116  stm32l4xx_hal_rcc.o(i.RCC_SetFlashLatencyFromMSIRange)
    i.SVC_Handler                            0x08001e6c   Section        0  stm32l4xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08001e6e   Section        0  stm32l4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08001e72   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08001ed4   Section        0  system_stm32l4xx.o(i.SystemInit)
    i.UsageFault_Handler                     0x08001ee4   Section        0  stm32l4xx_it.o(i.UsageFault_Handler)
    i.__NVIC_SetPriority                     0x08001ee6   Section        0  stm32l4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08001ee7   Thumb Code    32  stm32l4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.main                                   0x08001f08   Section        0  main.o(i.main)
    i.ssd1306_DrawPixel                      0x080020fc   Section        0  ssd1306.o(i.ssd1306_DrawPixel)
    i.ssd1306_Fill                           0x0800212c   Section        0  ssd1306.o(i.ssd1306_Fill)
    i.ssd1306_Init                           0x08002144   Section        0  ssd1306.o(i.ssd1306_Init)
    i.ssd1306_SetContrast                    0x0800220c   Section        0  ssd1306.o(i.ssd1306_SetContrast)
    i.ssd1306_SetCursor                      0x08002220   Section        0  ssd1306.o(i.ssd1306_SetCursor)
    i.ssd1306_SetDisplayOn                   0x0800222c   Section        0  ssd1306.o(i.ssd1306_SetDisplayOn)
    i.ssd1306_UpdateScreen                   0x08002244   Section        0  ssd1306.o(i.ssd1306_UpdateScreen)
    i.ssd1306_WriteChar                      0x0800227c   Section        0  ssd1306.o(i.ssd1306_WriteChar)
    i.ssd1306_WriteCommand                   0x0800232c   Section        0  ssd1306.o(i.ssd1306_WriteCommand)
    i.ssd1306_WriteData                      0x08002350   Section        0  ssd1306.o(i.ssd1306_WriteData)
    i.ssd1306_WriteString                    0x08002370   Section        0  ssd1306.o(i.ssd1306_WriteString)
    x$fpl$fpinit                             0x080023a4   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x080023a4   Number         0  fpinit.o(x$fpl$fpinit)
    .constdata                               0x080023b0   Section       28  main.o(.constdata)
    .constdata                               0x080023cc   Section     1900  ssd1306_fonts.o(.constdata)
    Font7x10                                 0x080023cc   Data        1900  ssd1306_fonts.o(.constdata)
    .constdata                               0x08002b38   Section     3420  ssd1306_fonts.o(.constdata)
    Font11x18                                0x08002b38   Data        3420  ssd1306_fonts.o(.constdata)
    .constdata                               0x08003894   Section     1520  ssd1306_fonts.o(.constdata)
    Font6x8                                  0x08003894   Data        1520  ssd1306_fonts.o(.constdata)
    .constdata                               0x08003e84   Section       12  ssd1306_fonts.o(.constdata)
    .constdata                               0x08003e90   Section       12  ssd1306_fonts.o(.constdata)
    .constdata                               0x08003e9c   Section       12  ssd1306_fonts.o(.constdata)
    .constdata                               0x08003ea8   Section       64  system_stm32l4xx.o(.constdata)
    .conststring                             0x08003ee8   Section       80  main.o(.conststring)
    .data                                    0x20000000   Section        6  ssd1306.o(.data)
    SSD1306                                  0x20000000   Data           6  ssd1306.o(.data)
    .data                                    0x20000008   Section        4  system_stm32l4xx.o(.data)
    .data                                    0x2000000c   Section       12  stm32l4xx_hal.o(.data)
    .bss                                     0x20000018   Section       84  main.o(.bss)
    .bss                                     0x2000006c   Section     1024  ssd1306.o(.bss)
    SSD1306_Buffer                           0x2000006c   Data        1024  ssd1306.o(.bss)
    .bss                                     0x2000046c   Section       96  libspace.o(.bss)
    HEAP                                     0x200004d0   Section      512  startup_stm32l432xx.o(HEAP)
    Heap_Mem                                 0x200004d0   Data         512  startup_stm32l432xx.o(HEAP)
    STACK                                    0x200006d0   Section     1024  startup_stm32l432xx.o(STACK)
    Stack_Mem                                0x200006d0   Data        1024  startup_stm32l432xx.o(STACK)
    __initial_sp                             0x20000ad0   Data           0  startup_stm32l432xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x0000018c   Number         0  startup_stm32l432xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32l432xx.o(RESET)
    __Vectors_End                            0x0800018c   Data           0  startup_stm32l432xx.o(RESET)
    __main                                   0x0800018d   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000195   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000195   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000195   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x080001a3   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c9   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e5   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_d                                0x08000201   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_percent                          0x08000201   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_u                                0x08000207   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_percent_end                      0x0800020d   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000211   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000213   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_alloca_1                   0x08000217   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000217   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000217   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000217   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000217   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000217   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000217   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000217   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x08000217   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x08000217   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000217   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000217   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x08000217   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000217   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x08000217   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000217   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x08000217   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000217   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000217   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x08000217   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x08000219   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0800021b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x0800021b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0800021b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x0800021b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x0800021b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x0800021b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x0800021b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x0800021b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x0800021d   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0800021d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0800021d   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000223   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000223   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000227   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000227   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800022f   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000231   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000231   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000235   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x0800023d   Thumb Code     8  startup_stm32l432xx.o(.text)
    ADC1_IRQHandler                          0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    CAN1_RX0_IRQHandler                      0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    CAN1_RX1_IRQHandler                      0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    CAN1_SCE_IRQHandler                      0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    CAN1_TX_IRQHandler                       0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    COMP_IRQHandler                          0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    CRS_IRQHandler                           0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    DMA1_Channel1_IRQHandler                 0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    DMA1_Channel2_IRQHandler                 0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    DMA1_Channel3_IRQHandler                 0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    DMA1_Channel4_IRQHandler                 0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    DMA1_Channel5_IRQHandler                 0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    DMA1_Channel6_IRQHandler                 0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    DMA1_Channel7_IRQHandler                 0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    DMA2_Channel1_IRQHandler                 0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    DMA2_Channel2_IRQHandler                 0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    DMA2_Channel3_IRQHandler                 0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    DMA2_Channel4_IRQHandler                 0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    DMA2_Channel5_IRQHandler                 0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    DMA2_Channel6_IRQHandler                 0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    DMA2_Channel7_IRQHandler                 0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    EXTI0_IRQHandler                         0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    EXTI15_10_IRQHandler                     0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    EXTI1_IRQHandler                         0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    EXTI2_IRQHandler                         0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    EXTI3_IRQHandler                         0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    EXTI4_IRQHandler                         0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    EXTI9_5_IRQHandler                       0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    FLASH_IRQHandler                         0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    FPU_IRQHandler                           0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    I2C1_ER_IRQHandler                       0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    I2C1_EV_IRQHandler                       0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    I2C3_ER_IRQHandler                       0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    I2C3_EV_IRQHandler                       0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    LPTIM1_IRQHandler                        0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    LPTIM2_IRQHandler                        0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    LPUART1_IRQHandler                       0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    PVD_PVM_IRQHandler                       0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    QUADSPI_IRQHandler                       0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    RNG_IRQHandler                           0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    RTC_Alarm_IRQHandler                     0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    RTC_WKUP_IRQHandler                      0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    SAI1_IRQHandler                          0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    SPI1_IRQHandler                          0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    SPI3_IRQHandler                          0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    SWPMI1_IRQHandler                        0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    TIM1_BRK_TIM15_IRQHandler                0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    TIM1_CC_IRQHandler                       0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    TIM1_UP_TIM16_IRQHandler                 0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    TIM2_IRQHandler                          0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    TIM6_DAC_IRQHandler                      0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    TIM7_IRQHandler                          0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    TSC_IRQHandler                           0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    USART1_IRQHandler                        0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    USART2_IRQHandler                        0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    USB_IRQHandler                           0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    WWDG_IRQHandler                          0x08000257   Thumb Code     0  startup_stm32l432xx.o(.text)
    __user_initial_stackheap                 0x08000259   Thumb Code     0  startup_stm32l432xx.o(.text)
    __2sprintf                               0x0800027d   Thumb Code    34  noretval__2sprintf.o(.text)
    _printf_int_dec                          0x080002a5   Thumb Code   104  _printf_dec.o(.text)
    __printf                                 0x0800031d   Thumb Code   184  __printf_ss.o(.text)
    __aeabi_memcpy4                          0x080003d5   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x080003d5   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x080003d5   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x0800041d   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memset                           0x08000439   Thumb Code    16  aeabi_memset.o(.text)
    __aeabi_memclr4                          0x08000449   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08000449   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08000449   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x0800044d   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x08000497   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000499   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0800049b   Thumb Code     2  heapauxi.o(.text)
    _printf_int_common                       0x0800049d   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_char_common                      0x0800055b   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000581   Thumb Code    10  _sputc.o(.text)
    __aeabi_memclr                           0x0800058b   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x0800058b   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x0800058f   Thumb Code     0  rt_memclr.o(.text)
    __user_libspace                          0x080005d1   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080005d1   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080005d1   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x080005d9   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000623   Thumb Code    18  exit.o(.text)
    _sys_exit                                0x08000635   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08000641   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000641   Thumb Code     2  use_no_semi.o(.text)
    BusFault_Handler                         0x08000643   Thumb Code     2  stm32l4xx_it.o(i.BusFault_Handler)
    __semihosting_library_function           0x08000643   Thumb Code     0  indicate_semi.o(.text)
    DHT11_Init                               0x0800066d   Thumb Code    62  dht11.o(i.DHT11_Init)
    DHT11_Read                               0x080006ab   Thumb Code   396  dht11.o(i.DHT11_Read)
    DebugMon_Handler                         0x0800083d   Thumb Code     2  stm32l4xx_it.o(i.DebugMon_Handler)
    Error_Handler                            0x0800083f   Thumb Code     4  main.o(i.Error_Handler)
    HAL_Delay                                0x08000845   Thumb Code    32  stm32l4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x08000869   Thumb Code   488  stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x08000a6b   Thumb Code    10  stm32l4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x08000a75   Thumb Code    10  stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08000a81   Thumb Code     6  stm32l4xx_hal.o(i.HAL_GetTick)
    HAL_I2CEx_ConfigAnalogFilter             0x08000a8d   Thumb Code    86  stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter)
    HAL_I2CEx_ConfigDigitalFilter            0x08000ae3   Thumb Code    82  stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter)
    HAL_I2C_Init                             0x08000b35   Thumb Code   184  stm32l4xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_Mem_Write                        0x08000bf1   Thumb Code   340  stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    HAL_I2C_MspInit                          0x08000d51   Thumb Code   114  stm32l4xx_hal_msp.o(i.HAL_I2C_MspInit)
    HAL_IncTick                              0x08000dd1   Thumb Code    12  stm32l4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08000de1   Thumb Code    30  stm32l4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08000e01   Thumb Code    58  stm32l4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08000e45   Thumb Code    56  stm32l4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08000e81   Thumb Code    26  stm32l4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08000e9d   Thumb Code    60  stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08000edd   Thumb Code    26  stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_PWREx_ControlVoltageScaling          0x08000f01   Thumb Code    90  stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling)
    HAL_PWREx_GetVoltageRange                0x08000f69   Thumb Code    10  stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange)
    HAL_RCCEx_PeriphCLKConfig                0x08000f79   Thumb Code   634  stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    HAL_RCC_ClockConfig                      0x08001205   Thumb Code   304  stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetSysClockFreq                  0x08001349   Thumb Code   150  stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x080013ed   Thumb Code  1366  stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x0800194d   Thumb Code    40  stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HardFault_Handler                        0x08001975   Thumb Code     2  stm32l4xx_it.o(i.HardFault_Handler)
    MemManage_Handler                        0x08001cb9   Thumb Code     2  stm32l4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08001cbb   Thumb Code     2  stm32l4xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x08001cbd   Thumb Code     2  stm32l4xx_it.o(i.PendSV_Handler)
    RCC_IRQHandler                           0x08001ded   Thumb Code     2  stm32l4xx_it.o(i.RCC_IRQHandler)
    SVC_Handler                              0x08001e6d   Thumb Code     2  stm32l4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08001e6f   Thumb Code     4  stm32l4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08001e73   Thumb Code    96  main.o(i.SystemClock_Config)
    SystemInit                               0x08001ed5   Thumb Code    12  system_stm32l4xx.o(i.SystemInit)
    UsageFault_Handler                       0x08001ee5   Thumb Code     2  stm32l4xx_it.o(i.UsageFault_Handler)
    main                                     0x08001f09   Thumb Code   394  main.o(i.main)
    ssd1306_DrawPixel                        0x080020fd   Thumb Code    42  ssd1306.o(i.ssd1306_DrawPixel)
    ssd1306_Fill                             0x0800212d   Thumb Code    18  ssd1306.o(i.ssd1306_Fill)
    ssd1306_Init                             0x08002145   Thumb Code   194  ssd1306.o(i.ssd1306_Init)
    ssd1306_SetContrast                      0x0800220d   Thumb Code    20  ssd1306.o(i.ssd1306_SetContrast)
    ssd1306_SetCursor                        0x08002221   Thumb Code     8  ssd1306.o(i.ssd1306_SetCursor)
    ssd1306_SetDisplayOn                     0x0800222d   Thumb Code    20  ssd1306.o(i.ssd1306_SetDisplayOn)
    ssd1306_UpdateScreen                     0x08002245   Thumb Code    50  ssd1306.o(i.ssd1306_UpdateScreen)
    ssd1306_WriteChar                        0x0800227d   Thumb Code   162  ssd1306.o(i.ssd1306_WriteChar)
    ssd1306_WriteCommand                     0x0800232d   Thumb Code    32  ssd1306.o(i.ssd1306_WriteCommand)
    ssd1306_WriteData                        0x08002351   Thumb Code    26  ssd1306.o(i.ssd1306_WriteData)
    ssd1306_WriteString                      0x08002371   Thumb Code    52  ssd1306.o(i.ssd1306_WriteString)
    _fp_init                                 0x080023a5   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x080023ad   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x080023ad   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    Font_6x8                                 0x08003e84   Data          12  ssd1306_fonts.o(.constdata)
    Font_7x10                                0x08003e90   Data          12  ssd1306_fonts.o(.constdata)
    Font_11x18                               0x08003e9c   Data          12  ssd1306_fonts.o(.constdata)
    AHBPrescTable                            0x08003ea8   Data          16  system_stm32l4xx.o(.constdata)
    MSIRangeTable                            0x08003eb8   Data          48  system_stm32l4xx.o(.constdata)
    Region$$Table$$Base                      0x08003f38   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08003f58   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000008   Data           4  system_stm32l4xx.o(.data)
    uwTickFreq                               0x2000000c   Data           1  stm32l4xx_hal.o(.data)
    uwTickPrio                               0x20000010   Data           4  stm32l4xx_hal.o(.data)
    uwTick                                   0x20000014   Data           4  stm32l4xx_hal.o(.data)
    hi2c1                                    0x20000018   Data          84  main.o(.bss)
    __libspace_start                         0x2000046c   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200004cc   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x0800018d

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00003f70, Max: 0x00040000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00003f58, Max: 0x00040000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x0000018c   Data   RO            3    RESET               startup_stm32l432xx.o
    0x0800018c   0x0800018c   0x00000008   Code   RO         2636  * !!!main             c_w.l(__main.o)
    0x08000194   0x08000194   0x00000034   Code   RO         2857    !!!scatter          c_w.l(__scatter.o)
    0x080001c8   0x080001c8   0x0000001a   Code   RO         2859    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001e2   0x080001e2   0x00000002   PAD
    0x080001e4   0x080001e4   0x0000001c   Code   RO         2861    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000200   0x08000200   0x00000000   Code   RO         2625    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x08000200   0x08000200   0x00000006   Code   RO         2623    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000206   0x08000206   0x00000006   Code   RO         2624    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x0800020c   0x0800020c   0x00000004   Code   RO         2671    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000210   0x08000210   0x00000002   Code   RO         2728    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000212   0x08000212   0x00000004   Code   RO         2731    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000216   0x08000216   0x00000000   Code   RO         2734    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000216   0x08000216   0x00000000   Code   RO         2737    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000216   0x08000216   0x00000000   Code   RO         2739    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000216   0x08000216   0x00000000   Code   RO         2741    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000216   0x08000216   0x00000000   Code   RO         2744    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000216   0x08000216   0x00000000   Code   RO         2746    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000216   0x08000216   0x00000000   Code   RO         2748    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000216   0x08000216   0x00000000   Code   RO         2750    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000216   0x08000216   0x00000000   Code   RO         2752    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000216   0x08000216   0x00000000   Code   RO         2754    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000216   0x08000216   0x00000000   Code   RO         2756    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000216   0x08000216   0x00000000   Code   RO         2758    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000216   0x08000216   0x00000000   Code   RO         2760    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000216   0x08000216   0x00000000   Code   RO         2762    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000216   0x08000216   0x00000000   Code   RO         2764    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000216   0x08000216   0x00000000   Code   RO         2768    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000216   0x08000216   0x00000000   Code   RO         2770    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000216   0x08000216   0x00000000   Code   RO         2772    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000216   0x08000216   0x00000000   Code   RO         2774    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000216   0x08000216   0x00000002   Code   RO         2775    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000218   0x08000218   0x00000002   Code   RO         2795    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x0800021a   0x0800021a   0x00000000   Code   RO         2808    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x0800021a   0x0800021a   0x00000000   Code   RO         2810    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x0800021a   0x0800021a   0x00000000   Code   RO         2812    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x0800021a   0x0800021a   0x00000000   Code   RO         2815    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x0800021a   0x0800021a   0x00000000   Code   RO         2818    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x0800021a   0x0800021a   0x00000000   Code   RO         2820    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x0800021a   0x0800021a   0x00000000   Code   RO         2823    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x0800021a   0x0800021a   0x00000002   Code   RO         2824    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x0800021c   0x0800021c   0x00000000   Code   RO         2662    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x0800021c   0x0800021c   0x00000000   Code   RO         2695    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x0800021c   0x0800021c   0x00000006   Code   RO         2707    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000222   0x08000222   0x00000000   Code   RO         2697    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000222   0x08000222   0x00000004   Code   RO         2698    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000226   0x08000226   0x00000000   Code   RO         2700    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000226   0x08000226   0x00000008   Code   RO         2701    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800022e   0x0800022e   0x00000002   Code   RO         2729    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000230   0x08000230   0x00000000   Code   RO         2777    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000230   0x08000230   0x00000004   Code   RO         2778    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000234   0x08000234   0x00000006   Code   RO         2779    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x0800023a   0x0800023a   0x00000002   PAD
    0x0800023c   0x0800023c   0x00000040   Code   RO            4    .text               startup_stm32l432xx.o
    0x0800027c   0x0800027c   0x00000028   Code   RO         2597    .text               c_w.l(noretval__2sprintf.o)
    0x080002a4   0x080002a4   0x00000078   Code   RO         2601    .text               c_w.l(_printf_dec.o)
    0x0800031c   0x0800031c   0x000000b8   Code   RO         2606    .text               c_w.l(__printf_ss.o)
    0x080003d4   0x080003d4   0x00000064   Code   RO         2628    .text               c_w.l(rt_memcpy_w.o)
    0x08000438   0x08000438   0x00000010   Code   RO         2630    .text               c_w.l(aeabi_memset.o)
    0x08000448   0x08000448   0x0000004e   Code   RO         2632    .text               c_w.l(rt_memclr_w.o)
    0x08000496   0x08000496   0x00000006   Code   RO         2634    .text               c_w.l(heapauxi.o)
    0x0800049c   0x0800049c   0x000000b2   Code   RO         2665    .text               c_w.l(_printf_intcommon.o)
    0x0800054e   0x0800054e   0x00000002   PAD
    0x08000550   0x08000550   0x00000030   Code   RO         2667    .text               c_w.l(_printf_char_common.o)
    0x08000580   0x08000580   0x0000000a   Code   RO         2669    .text               c_w.l(_sputc.o)
    0x0800058a   0x0800058a   0x00000044   Code   RO         2672    .text               c_w.l(rt_memclr.o)
    0x080005ce   0x080005ce   0x00000002   PAD
    0x080005d0   0x080005d0   0x00000008   Code   RO         2716    .text               c_w.l(libspace.o)
    0x080005d8   0x080005d8   0x0000004a   Code   RO         2719    .text               c_w.l(sys_stackheap_outer.o)
    0x08000622   0x08000622   0x00000012   Code   RO         2721    .text               c_w.l(exit.o)
    0x08000634   0x08000634   0x0000000c   Code   RO         2787    .text               c_w.l(sys_exit.o)
    0x08000640   0x08000640   0x00000002   Code   RO         2798    .text               c_w.l(use_no_semi.o)
    0x08000642   0x08000642   0x00000000   Code   RO         2800    .text               c_w.l(indicate_semi.o)
    0x08000642   0x08000642   0x00000002   Code   RO          164    i.BusFault_Handler  stm32l4xx_it.o
    0x08000644   0x08000644   0x00000028   Code   RO          281    i.DHT11_DelayUs     dht11.o
    0x0800066c   0x0800066c   0x0000003e   Code   RO          282    i.DHT11_Init        dht11.o
    0x080006aa   0x080006aa   0x00000192   Code   RO          283    i.DHT11_Read        dht11.o
    0x0800083c   0x0800083c   0x00000002   Code   RO          165    i.DebugMon_Handler  stm32l4xx_it.o
    0x0800083e   0x0800083e   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x08000842   0x08000842   0x00000002   PAD
    0x08000844   0x08000844   0x00000024   Code   RO         1136    i.HAL_Delay         stm32l4xx_hal.o
    0x08000868   0x08000868   0x00000202   Code   RO         1829    i.HAL_GPIO_Init     stm32l4xx_hal_gpio.o
    0x08000a6a   0x08000a6a   0x0000000a   Code   RO         1831    i.HAL_GPIO_ReadPin  stm32l4xx_hal_gpio.o
    0x08000a74   0x08000a74   0x0000000a   Code   RO         1833    i.HAL_GPIO_WritePin  stm32l4xx_hal_gpio.o
    0x08000a7e   0x08000a7e   0x00000002   PAD
    0x08000a80   0x08000a80   0x0000000c   Code   RO         1140    i.HAL_GetTick       stm32l4xx_hal.o
    0x08000a8c   0x08000a8c   0x00000056   Code   RO         1075    i.HAL_I2CEx_ConfigAnalogFilter  stm32l4xx_hal_i2c_ex.o
    0x08000ae2   0x08000ae2   0x00000052   Code   RO         1076    i.HAL_I2CEx_ConfigDigitalFilter  stm32l4xx_hal_i2c_ex.o
    0x08000b34   0x08000b34   0x000000bc   Code   RO          614    i.HAL_I2C_Init      stm32l4xx_hal_i2c.o
    0x08000bf0   0x08000bf0   0x0000015e   Code   RO          635    i.HAL_I2C_Mem_Write  stm32l4xx_hal_i2c.o
    0x08000d4e   0x08000d4e   0x00000002   PAD
    0x08000d50   0x08000d50   0x00000080   Code   RO          246    i.HAL_I2C_MspInit   stm32l4xx_hal_msp.o
    0x08000dd0   0x08000dd0   0x00000010   Code   RO         1146    i.HAL_IncTick       stm32l4xx_hal.o
    0x08000de0   0x08000de0   0x0000001e   Code   RO         1147    i.HAL_Init          stm32l4xx_hal.o
    0x08000dfe   0x08000dfe   0x00000002   PAD
    0x08000e00   0x08000e00   0x00000044   Code   RO         1148    i.HAL_InitTick      stm32l4xx_hal.o
    0x08000e44   0x08000e44   0x0000003c   Code   RO          247    i.HAL_MspInit       stm32l4xx_hal_msp.o
    0x08000e80   0x08000e80   0x0000001a   Code   RO         2348    i.HAL_NVIC_EnableIRQ  stm32l4xx_hal_cortex.o
    0x08000e9a   0x08000e9a   0x00000002   PAD
    0x08000e9c   0x08000e9c   0x00000040   Code   RO         2354    i.HAL_NVIC_SetPriority  stm32l4xx_hal_cortex.o
    0x08000edc   0x08000edc   0x00000024   Code   RO         2355    i.HAL_NVIC_SetPriorityGrouping  stm32l4xx_hal_cortex.o
    0x08000f00   0x08000f00   0x00000068   Code   RO         2120    i.HAL_PWREx_ControlVoltageScaling  stm32l4xx_hal_pwr_ex.o
    0x08000f68   0x08000f68   0x00000010   Code   RO         2147    i.HAL_PWREx_GetVoltageRange  stm32l4xx_hal_pwr_ex.o
    0x08000f78   0x08000f78   0x0000028a   Code   RO         1462    i.HAL_RCCEx_PeriphCLKConfig  stm32l4xx_hal_rcc_ex.o
    0x08001202   0x08001202   0x00000002   PAD
    0x08001204   0x08001204   0x00000144   Code   RO         1334    i.HAL_RCC_ClockConfig  stm32l4xx_hal_rcc.o
    0x08001348   0x08001348   0x000000a4   Code   RO         1343    i.HAL_RCC_GetSysClockFreq  stm32l4xx_hal_rcc.o
    0x080013ec   0x080013ec   0x00000560   Code   RO         1346    i.HAL_RCC_OscConfig  stm32l4xx_hal_rcc.o
    0x0800194c   0x0800194c   0x00000028   Code   RO         2359    i.HAL_SYSTICK_Config  stm32l4xx_hal_cortex.o
    0x08001974   0x08001974   0x00000002   Code   RO          166    i.HardFault_Handler  stm32l4xx_it.o
    0x08001976   0x08001976   0x00000022   Code   RO          661    i.I2C_Flush_TXDR    stm32l4xx_hal_i2c.o
    0x08001998   0x08001998   0x0000010c   Code   RO          669    i.I2C_IsErrorOccurred  stm32l4xx_hal_i2c.o
    0x08001aa4   0x08001aa4   0x00000064   Code   RO          675    i.I2C_RequestMemoryWrite  stm32l4xx_hal_i2c.o
    0x08001b08   0x08001b08   0x00000030   Code   RO          678    i.I2C_TransferConfig  stm32l4xx_hal_i2c.o
    0x08001b38   0x08001b38   0x0000007a   Code   RO          680    i.I2C_WaitOnFlagUntilTimeout  stm32l4xx_hal_i2c.o
    0x08001bb2   0x08001bb2   0x00000056   Code   RO          682    i.I2C_WaitOnSTOPFlagUntilTimeout  stm32l4xx_hal_i2c.o
    0x08001c08   0x08001c08   0x0000005a   Code   RO          683    i.I2C_WaitOnTXISFlagUntilTimeout  stm32l4xx_hal_i2c.o
    0x08001c62   0x08001c62   0x00000002   PAD
    0x08001c64   0x08001c64   0x00000054   Code   RO           14    i.MX_I2C1_Init      main.o
    0x08001cb8   0x08001cb8   0x00000002   Code   RO          167    i.MemManage_Handler  stm32l4xx_it.o
    0x08001cba   0x08001cba   0x00000002   Code   RO          168    i.NMI_Handler       stm32l4xx_it.o
    0x08001cbc   0x08001cbc   0x00000002   Code   RO          169    i.PendSV_Handler    stm32l4xx_it.o
    0x08001cbe   0x08001cbe   0x00000002   PAD
    0x08001cc0   0x08001cc0   0x0000012c   Code   RO         1466    i.RCCEx_PLLSAI1_Config  stm32l4xx_hal_rcc_ex.o
    0x08001dec   0x08001dec   0x00000002   Code   RO          170    i.RCC_IRQHandler    stm32l4xx_it.o
    0x08001dee   0x08001dee   0x00000002   PAD
    0x08001df0   0x08001df0   0x0000007c   Code   RO         1347    i.RCC_SetFlashLatencyFromMSIRange  stm32l4xx_hal_rcc.o
    0x08001e6c   0x08001e6c   0x00000002   Code   RO          171    i.SVC_Handler       stm32l4xx_it.o
    0x08001e6e   0x08001e6e   0x00000004   Code   RO          172    i.SysTick_Handler   stm32l4xx_it.o
    0x08001e72   0x08001e72   0x00000060   Code   RO           15    i.SystemClock_Config  main.o
    0x08001ed2   0x08001ed2   0x00000002   PAD
    0x08001ed4   0x08001ed4   0x00000010   Code   RO          566    i.SystemInit        system_stm32l4xx.o
    0x08001ee4   0x08001ee4   0x00000002   Code   RO          173    i.UsageFault_Handler  stm32l4xx_it.o
    0x08001ee6   0x08001ee6   0x00000020   Code   RO         2361    i.__NVIC_SetPriority  stm32l4xx_hal_cortex.o
    0x08001f06   0x08001f06   0x00000002   PAD
    0x08001f08   0x08001f08   0x000001f4   Code   RO           16    i.main              main.o
    0x080020fc   0x080020fc   0x00000030   Code   RO          326    i.ssd1306_DrawPixel  ssd1306.o
    0x0800212c   0x0800212c   0x00000018   Code   RO          328    i.ssd1306_Fill      ssd1306.o
    0x08002144   0x08002144   0x000000c8   Code   RO          333    i.ssd1306_Init      ssd1306.o
    0x0800220c   0x0800220c   0x00000014   Code   RO          339    i.ssd1306_SetContrast  ssd1306.o
    0x08002220   0x08002220   0x0000000c   Code   RO          340    i.ssd1306_SetCursor  ssd1306.o
    0x0800222c   0x0800222c   0x00000018   Code   RO          341    i.ssd1306_SetDisplayOn  ssd1306.o
    0x08002244   0x08002244   0x00000038   Code   RO          343    i.ssd1306_UpdateScreen  ssd1306.o
    0x0800227c   0x0800227c   0x000000ae   Code   RO          344    i.ssd1306_WriteChar  ssd1306.o
    0x0800232a   0x0800232a   0x00000002   PAD
    0x0800232c   0x0800232c   0x00000024   Code   RO          345    i.ssd1306_WriteCommand  ssd1306.o
    0x08002350   0x08002350   0x00000020   Code   RO          346    i.ssd1306_WriteData  ssd1306.o
    0x08002370   0x08002370   0x00000034   Code   RO          347    i.ssd1306_WriteString  ssd1306.o
    0x080023a4   0x080023a4   0x0000000a   Code   RO         2785    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x080023ae   0x080023ae   0x00000002   PAD
    0x080023b0   0x080023b0   0x0000001c   Data   RO           18    .constdata          main.o
    0x080023cc   0x080023cc   0x0000076c   Data   RO          520    .constdata          ssd1306_fonts.o
    0x08002b38   0x08002b38   0x00000d5c   Data   RO          521    .constdata          ssd1306_fonts.o
    0x08003894   0x08003894   0x000005f0   Data   RO          522    .constdata          ssd1306_fonts.o
    0x08003e84   0x08003e84   0x0000000c   Data   RO          523    .constdata          ssd1306_fonts.o
    0x08003e90   0x08003e90   0x0000000c   Data   RO          524    .constdata          ssd1306_fonts.o
    0x08003e9c   0x08003e9c   0x0000000c   Data   RO          525    .constdata          ssd1306_fonts.o
    0x08003ea8   0x08003ea8   0x00000040   Data   RO          567    .constdata          system_stm32l4xx.o
    0x08003ee8   0x08003ee8   0x00000050   Data   RO           19    .conststring        main.o
    0x08003f38   0x08003f38   0x00000020   Data   RO         2855    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x10000000, Load base: 0x08003f70, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08003f58, Size: 0x00000ad0, Max: 0x0000c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08003f58   0x00000006   Data   RW          349    .data               ssd1306.o
    0x20000006   0x08003f5e   0x00000002   PAD
    0x20000008   0x08003f60   0x00000004   Data   RW          569    .data               system_stm32l4xx.o
    0x2000000c   0x08003f64   0x0000000c   Data   RW         1159    .data               stm32l4xx_hal.o
    0x20000018        -       0x00000054   Zero   RW           17    .bss                main.o
    0x2000006c        -       0x00000400   Zero   RW          348    .bss                ssd1306.o
    0x2000046c        -       0x00000060   Zero   RW         2717    .bss                c_w.l(libspace.o)
    0x200004cc   0x08003f70   0x00000004   PAD
    0x200004d0        -       0x00000200   Zero   RW            2    HEAP                startup_stm32l432xx.o
    0x200006d0        -       0x00000400   Zero   RW            1    STACK               startup_stm32l432xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       504         12          0          0          0       2750   dht11.o
       684        118        108          0         84     613924   main.o
       678         48          0          6       1024       8581   ssd1306.o
         0          0       6876          0          0        640   ssd1306_fonts.o
        64         26        396          0       1536        828   startup_stm32l432xx.o
       162         24          0         12          0      11449   stm32l4xx_hal.o
       198         14          0          0          0      33747   stm32l4xx_hal_cortex.o
       534         20          0          0          0       2863   stm32l4xx_hal_gpio.o
      1286         22          0          0          0      10043   stm32l4xx_hal_i2c.o
       168          0          0          0          0       1863   stm32l4xx_hal_i2c_ex.o
       188         18          0          0          0       1588   stm32l4xx_hal_msp.o
       120         20          0          0          0       1334   stm32l4xx_hal_pwr_ex.o
      1988         74          0          0          0       5718   stm32l4xx_hal_rcc.o
       950         32          0          0          0       2848   stm32l4xx_hal_rcc_ex.o
        22          0          0          0          0       4467   stm32l4xx_it.o
        16          4         64          4          0       1137   system_stm32l4xx.o

    ----------------------------------------------------------------------
      7586        <USER>       <GROUP>         24       2644     703780   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        24          0          0          2          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       184          0          0          0          0         84   __printf_ss.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        48          6          0          0          0         96   _printf_char_common.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
       178          0          0          0          0         88   _printf_intcommon.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_u.o
        10          0          0          0          0         68   _sputc.o
        16          0          0          0          0         68   aeabi_memset.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         6          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        40          6          0          0          0         84   noretval__2sprintf.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        10          0          0          0          0        116   fpinit.o

    ----------------------------------------------------------------------
      1154         <USER>          <GROUP>          0        100       1508   Library Totals
        10          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1134         44          0          0         96       1392   c_w.l
        10          0          0          0          0        116   fz_wm.l

    ----------------------------------------------------------------------
      1154         <USER>          <GROUP>          0        100       1508   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      8740        476       7476         24       2744     699012   Grand Totals
      8740        476       7476         24       2744     699012   ELF Image Totals
      8740        476       7476         24          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                16216 (  15.84kB)
    Total RW  Size (RW Data + ZI Data)              2768 (   2.70kB)
    Total ROM Size (Code + RO Data + RW Data)      16240 (  15.86kB)

==============================================================================

