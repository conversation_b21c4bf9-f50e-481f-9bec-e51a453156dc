#!/usr/bin/env python3
"""
图片转换为OLED位图数据的Python脚本
使用方法: python image_converter.py input.jpg output.c
"""

from PIL import Image
import sys
import os

def convert_image_to_bitmap(input_path, output_path, width=128, height=64):
    """
    将图片转换为OLED位图数据
    """
    try:
        # 打开图片
        img = Image.open(input_path)
        print(f"原始图片尺寸: {img.size}")
        
        # 调整尺寸
        img = img.resize((width, height), Image.Resampling.LANCZOS)
        
        # 转换为灰度
        img = img.convert('L')
        
        # 转换为黑白二值图像
        threshold = 128  # 阈值，可以调整
        img = img.point(lambda x: 255 if x > threshold else 0, mode='1')
        
        # 获取像素数据
        pixels = list(img.getdata())
        
        # 转换为位图数据
        bitmap_data = []
        for y in range(0, height, 8):  # OLED按8像素为一组垂直排列
            for x in range(width):
                byte_value = 0
                for bit in range(8):
                    if y + bit < height:
                        pixel_index = (y + bit) * width + x
                        if pixels[pixel_index]:  # 白色像素
                            byte_value |= (1 << bit)
                bitmap_data.append(byte_value)
        
        # 生成C代码
        array_name = os.path.splitext(os.path.basename(input_path))[0].replace('-', '_').replace(' ', '_')
        
        c_code = f"""/**
 * 自动生成的位图数据
 * 原始文件: {input_path}
 * 尺寸: {width}x{height}
 */

#include "bitmap_data.h"

const uint8_t {array_name}_{width}x{height}[{len(bitmap_data)}] = {{
"""
        
        # 添加数据，每行16个字节
        for i in range(0, len(bitmap_data), 16):
            line = "    "
            for j in range(16):
                if i + j < len(bitmap_data):
                    line += f"0x{bitmap_data[i + j]:02X}"
                    if i + j < len(bitmap_data) - 1:
                        line += ", "
            c_code += line + "\n"
        
        c_code += "};\n"
        
        # 写入文件
        with open(output_path, 'w') as f:
            f.write(c_code)
        
        print(f"转换完成！")
        print(f"输出文件: {output_path}")
        print(f"数组名称: {array_name}_{width}x{height}")
        print(f"数据大小: {len(bitmap_data)} 字节")
        
        # 生成预览
        preview_path = output_path.replace('.c', '_preview.png')
        img.save(preview_path)
        print(f"预览图片: {preview_path}")
        
    except Exception as e:
        print(f"转换失败: {e}")

def main():
    if len(sys.argv) < 3:
        print("使用方法: python image_converter.py <输入图片> <输出C文件> [宽度] [高度]")
        print("示例: python image_converter.py photo.jpg photo_data.c 128 64")
        return
    
    input_path = sys.argv[1]
    output_path = sys.argv[2]
    width = int(sys.argv[3]) if len(sys.argv) > 3 else 128
    height = int(sys.argv[4]) if len(sys.argv) > 4 else 64
    
    if not os.path.exists(input_path):
        print(f"输入文件不存在: {input_path}")
        return
    
    convert_image_to_bitmap(input_path, output_path, width, height)

if __name__ == "__main__":
    main()
