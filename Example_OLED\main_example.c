/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body - OLED Display Example
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "ssd1306.h"
#include "ssd1306_fonts.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
I2C_HandleTypeDef hi2c1;

/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
static void MX_GPIO_Init(void);
static void MX_I2C1_Init(void);

/* USER CODE BEGIN PFP */
void OLED_DisplayDemo(void);
void OLED_DisplayText(void);
void OLED_DisplayGraphics(void);
/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{
  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_I2C1_Init();
  /* USER CODE BEGIN 2 */

  // 初始化OLED
  ssd1306_Init();
  
  // 清屏
  ssd1306_Fill(Black);
  ssd1306_UpdateScreen();
  
  HAL_Delay(1000);

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
    
    // 显示演示
    OLED_DisplayDemo();
    
    HAL_Delay(3000);
    
    // 显示文字
    OLED_DisplayText();
    
    HAL_Delay(3000);
    
    // 显示图形
    OLED_DisplayGraphics();
    
    HAL_Delay(3000);
  }
  /* USER CODE END 3 */
}

/* USER CODE BEGIN 4 */

/**
 * @brief OLED显示演示
 */
void OLED_DisplayDemo(void)
{
    // 清屏
    ssd1306_Fill(Black);
    
    // 显示标题
    ssd1306_SetCursor(2, 0);
    ssd1306_WriteString("STM32L432KC", Font_11x18, White);
    
    ssd1306_SetCursor(15, 20);
    ssd1306_WriteString("OLED Demo", Font_7x10, White);
    
    // 显示版本信息
    ssd1306_SetCursor(10, 35);
    ssd1306_WriteString("Version 1.0", Font_6x8, White);
    
    // 显示日期
    ssd1306_SetCursor(20, 50);
    ssd1306_WriteString("2024-12-02", Font_6x8, White);
    
    // 更新屏幕
    ssd1306_UpdateScreen();
}

/**
 * @brief 显示各种文字
 */
void OLED_DisplayText(void)
{
    // 清屏
    ssd1306_Fill(Black);
    
    // 不同字体显示
    ssd1306_SetCursor(2, 0);
    ssd1306_WriteString("Font 6x8", Font_6x8, White);
    
    ssd1306_SetCursor(2, 12);
    ssd1306_WriteString("Font 7x10", Font_7x10, White);
    
    ssd1306_SetCursor(2, 25);
    ssd1306_WriteString("Font 11x18", Font_11x18, White);
    
    ssd1306_SetCursor(2, 45);
    ssd1306_WriteString("Hello World!", Font_7x10, White);
    
    // 更新屏幕
    ssd1306_UpdateScreen();
}

/**
 * @brief 显示图形
 */
void OLED_DisplayGraphics(void)
{
    // 清屏
    ssd1306_Fill(Black);
    
    // 绘制矩形
    ssd1306_DrawRectangle(10, 10, 50, 30, White);
    
    // 绘制填充矩形
    ssd1306_FillRectangle(70, 10, 110, 30, White);
    
    // 绘制圆形
    ssd1306_DrawCircle(30, 45, 10, White);
    
    // 绘制线条
    ssd1306_Line(60, 35, 100, 55, White);
    
    // 绘制像素点
    for(int i = 0; i < 20; i++) {
        ssd1306_DrawPixel(110 + (i % 5), 40 + (i / 5), White);
    }
    
    // 更新屏幕
    ssd1306_UpdateScreen();
}

/* USER CODE END 4 */
