--cpu=Cortex-M4.fp.sp --branchpatch=sdcomp-29491-629360
"002\startup_stm32l432xx.o"
"002\main.o"
"002\stm32l4xx_it.o"
"002\stm32l4xx_hal_msp.o"
"002\dht11.o"
"002\ssd1306.o"
"002\ssd1306_fonts.o"
"002\bmp_images.o"
"002\system_stm32l4xx.o"
"002\stm32l4xx_hal_i2c.o"
"002\stm32l4xx_hal_i2c_ex.o"
"002\stm32l4xx_hal.o"
"002\stm32l4xx_hal_rcc.o"
"002\stm32l4xx_hal_rcc_ex.o"
"002\stm32l4xx_hal_flash.o"
"002\stm32l4xx_hal_flash_ex.o"
"002\stm32l4xx_hal_flash_ramfunc.o"
"002\stm32l4xx_hal_gpio.o"
"002\stm32l4xx_hal_dma.o"
"002\stm32l4xx_hal_dma_ex.o"
"002\stm32l4xx_hal_pwr.o"
"002\stm32l4xx_hal_pwr_ex.o"
"002\stm32l4xx_hal_cortex.o"
"002\stm32l4xx_hal_exti.o"
"002\stm32l4xx_hal_tim.o"
"002\stm32l4xx_hal_tim_ex.o"
--strict --scatter "002\002.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "002.map" -o 002\002.axf