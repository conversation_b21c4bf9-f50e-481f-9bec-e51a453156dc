/**
 * @file bitmap_data.c
 * @brief 位图数据定义
 */

#include "bitmap_data.h"

// 示例：简单的笑脸图案 (32x32像素)
const uint8_t smiley_32x32[128] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};

// 示例：温度计图标 (16x16像素)
const uint8_t thermometer_16x16[32] = {
    0x01, 0x80, 0x02, 0x40, 0x02, 0x40, 0x02, 0x40, 0x02, 0x40, 0x02, 0x40, 0x02, 0x40, 0x02, 0x40,
    0x02, 0x40, 0x02, 0x40, 0x02, 0x40, 0x02, 0x40, 0x02, 0x40, 0x07, 0xE0, 0x0F, 0xF0, 0x07, 0xE0
};

// 示例：湿度计图标 (16x16像素)
const uint8_t humidity_16x16[32] = {
    0x01, 0x80, 0x03, 0xC0, 0x07, 0xE0, 0x0F, 0xF0, 0x1F, 0xF8, 0x3F, 0xFC, 0x7F, 0xFE, 0x7F, 0xFE,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7F, 0xFE, 0x7F, 0xFE, 0x3F, 0xFC, 0x1F, 0xF8, 0x07, 0xE0
};

// 示例：WiFi信号图标 (16x16像素)
const uint8_t wifi_16x16[32] = {
    0x00, 0x00, 0x00, 0x00, 0x07, 0xE0, 0x18, 0x18, 0x20, 0x04, 0x47, 0xE2, 0x88, 0x11, 0x90, 0x09,
    0xA1, 0x85, 0xA2, 0x45, 0xA4, 0x25, 0xA8, 0x15, 0xB0, 0x0D, 0xA0, 0x05, 0x40, 0x02, 0x80, 0x01
};

// 示例：电池图标 (24x12像素)
const uint8_t battery_24x12[36] = {
    0xFF, 0xFF, 0xF0, 0x80, 0x00, 0x18, 0x80, 0x00, 0x18, 0x80, 0x00, 0x18, 0x80, 0x00, 0x18,
    0x80, 0x00, 0x18, 0x80, 0x00, 0x18, 0x80, 0x00, 0x18, 0x80, 0x00, 0x18, 0x80, 0x00, 0x18,
    0x80, 0x00, 0x18, 0xFF, 0xFF, 0xF0
};

// 示例：全屏启动画面 (128x64像素)
const uint8_t startup_screen_128x64[1024] = {
    // 这里应该是1024字节的位图数据
    // 由于篇幅限制，这里只显示前几行
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    // ... 继续添加剩余的位图数据
    // 实际使用时，这里应该包含完整的1024字节数据
};

// 示例：公司Logo (64x32像素)
const uint8_t company_logo_64x32[256] = {
    // 这里应该是256字节的位图数据
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    // ... 继续添加剩余的位图数据
};

// 动画帧数组示例
const uint8_t* animation_frames[] = {
    smiley_32x32,
    thermometer_16x16,
    humidity_16x16,
    wifi_16x16
};

const uint8_t animation_frame_count = sizeof(animation_frames) / sizeof(animation_frames[0]);
