/**
 * @file debug_test.c
 * @brief 调试测试函数
 */

#include "main.h"
#include "ssd1306.h"
#include "ssd1306_fonts.h"
#include "DHT11.h"

extern I2C_HandleTypeDef hi2c1;

/**
 * @brief 测试OLED显示功能
 */
void Test_OLED_Display(void)
{
    // 测试基本显示
    ssd1306_Fill(Black);
    ssd1306_SetCursor(2, 0);
    ssd1306_WriteString("OLED Test", Font_11x18, White);
    ssd1306_SetCursor(2, 20);
    ssd1306_WriteString("Display OK", Font_7x10, White);
    ssd1306_UpdateScreen();
    HAL_Delay(2000);
    
    // 测试不同字体
    ssd1306_Fill(Black);
    ssd1306_SetCursor(2, 0);
    ssd1306_WriteString("Font 6x8", Font_6x8, <PERSON>);
    ssd1306_SetCursor(2, 15);
    ssd1306_WriteString("Font 7x10", Font_7x10, White);
    ssd1306_SetCursor(2, 30);
    ssd1306_WriteString("Font 11x18", Font_11x18, White);
    ssd1306_UpdateScreen();
    HAL_Delay(2000);
}

/**
 * @brief 测试I2C通信
 */
uint8_t Test_I2C_Communication(void)
{
    uint8_t test_data = 0x00;
    HAL_StatusTypeDef status;
    
    // 尝试与OLED通信
    status = HAL_I2C_IsDeviceReady(&hi2c1, SSD1306_I2C_ADDR, 3, 100);
    
    if (status == HAL_OK) {
        return 0; // 成功
    } else {
        return 1; // 失败
    }
}

/**
 * @brief 测试DHT11传感器
 */
void Test_DHT11_Sensor(void)
{
    uint8_t temp, humi;
    char buf[32];
    
    ssd1306_Fill(Black);
    ssd1306_SetCursor(2, 0);
    ssd1306_WriteString("DHT11 Test", Font_11x18, White);
    
    for (int i = 0; i < 5; i++) {
        uint8_t result = DHT11_Read(&temp, &humi);
        
        sprintf(buf, "Try %d: %s", i+1, (result == 0) ? "OK" : "FAIL");
        ssd1306_SetCursor(2, 20 + i*8);
        ssd1306_WriteString(buf, Font_6x8, White);
        ssd1306_UpdateScreen();
        
        if (result == 0) {
            sprintf(buf, "T:%dC H:%d%%", temp, humi);
            ssd1306_SetCursor(2, 50);
            ssd1306_WriteString(buf, Font_7x10, White);
            ssd1306_UpdateScreen();
            break;
        }
        
        HAL_Delay(2000);
    }
    
    HAL_Delay(3000);
}

/**
 * @brief 系统诊断测试
 */
void System_Diagnostic(void)
{
    char buf[32];
    
    ssd1306_Fill(Black);
    ssd1306_SetCursor(2, 0);
    ssd1306_WriteString("System Info", Font_11x18, White);
    
    // 显示系统时钟
    sprintf(buf, "CLK:%luMHz", SystemCoreClock/1000000);
    ssd1306_SetCursor(2, 20);
    ssd1306_WriteString(buf, Font_7x10, White);
    
    // 测试I2C
    uint8_t i2c_status = Test_I2C_Communication();
    sprintf(buf, "I2C:%s", (i2c_status == 0) ? "OK" : "FAIL");
    ssd1306_SetCursor(2, 30);
    ssd1306_WriteString(buf, Font_7x10, White);
    
    // 显示DWT状态
    sprintf(buf, "DWT:%s", (DWT->CTRL & DWT_CTRL_CYCCNTENA_Msk) ? "ON" : "OFF");
    ssd1306_SetCursor(2, 40);
    ssd1306_WriteString(buf, Font_7x10, White);
    
    ssd1306_UpdateScreen();
    HAL_Delay(3000);
}
